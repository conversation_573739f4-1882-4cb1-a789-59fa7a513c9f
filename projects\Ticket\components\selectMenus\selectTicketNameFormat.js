const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle, StringSelectMenuBuilder, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'select_ticket_name_format',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      if (!interaction.replied && !interaction.deferred) {
        return interaction.reply({
          content: errorMessage,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // تحديث تنسيق اسم التذكرة المحدد في الحالة المؤقتة (لن يتم الحفظ في قاعدة البيانات حتى يتم الضغط على زر الحفظ)
    const selectedFormat = interaction.values[0];
    state.ticketNameFormat = selectedFormat;
    state.hasUnsavedChanges = true; // تعيين علامة التغييرات غير المحفوظة
    setupStates.set(interaction.user.id, state);

    // طباعة معلومات التشخيص
    console.log(`تم اختيار تنسيق اسم التذكرة: ${selectedFormat} (لم يتم الحفظ بعد)`);

    // الحصول على معلومات اللوحة من قاعدة البيانات
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      if (!interaction.replied && !interaction.deferred) {
        return interaction.reply({
          content: errorMessage,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.description', { panel_name: selectedPanel.name });
    const usernameFormat = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.username_format');
    const numberedFormat = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.numbered_format');
    const currentFormatLabel = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.current_format');
    const backButton = await client.translate(interaction.guild.id, 'panel_editor.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'panel_editor.save_button');

    // طباعة الترجمات للتشخيص
    console.log('الترجمات:');
    console.log(`العنوان: ${title}`);
    console.log(`الوصف: ${description}`);
    console.log(`تنسيق اسم المستخدم: ${usernameFormat}`);
    console.log(`تنسيق الترقيم: ${numberedFormat}`);
    console.log(`التنسيق الحالي: ${currentFormatLabel}`);
    console.log(`زر الرجوع: ${backButton}`);
    console.log(`زر الحفظ: ${saveButton}`);

    // تحديد النص المعروض للتنسيق المحدد
    let currentFormatText = '';
    if (selectedFormat === 'username') {
      currentFormatText = usernameFormat;
    } else if (selectedFormat === 'numbered') {
      currentFormatText = numberedFormat;
    }

    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description)
      .addFields({ name: currentFormatLabel, value: currentFormatText });

    // إنشاء قائمة منسدلة لاختيار تنسيق اسم التذكرة
    const selectMenu = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_ticket_name_format')
          .setPlaceholder('اختر تنسيق اسم التذكرة')
          .addOptions([
            {
              label: usernameFormat,
              value: 'username',
              description: await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.username_description'),
              default: selectedFormat === 'username'
            },
            {
              label: numberedFormat,
              value: 'numbered',
              description: await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.numbered_description'),
              default: selectedFormat === 'numbered'
            }
          ])
      );

    // إنشاء صف الأزرار (رجوع وحفظ)
    const buttonsRow = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId(`back_to_panel_action_${state.selectedPanel}`)
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId(`save_ticket_name_format_${state.selectedPanel}_${selectedFormat}`)
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Success)
      );

    // تحديث الواجهة مع إبقاء القائمة المنسدلة وإضافة أزرار الحفظ والرجوع
    await interaction.update({
      embeds: [embed],
      components: [selectMenu, buttonsRow]
    });
  }
};
