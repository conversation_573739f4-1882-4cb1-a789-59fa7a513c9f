const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, StringSelectMenuBuilder, ButtonBuilder, ButtonStyle, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');
// تم تعطيل استيراد الخدمات مؤقتًا
// const { panelService } = require('../../services');

module.exports = {
  id: 'save_ticket_name_format',

  async execute(interaction, client) {
    try {
      // استخراج معرف اللوحة والتنسيق المحدد من معرف الزر
      const customIdParts = interaction.customId.split('_');
      const panelId = customIdParts[4];
      const selectedFormat = customIdParts[5];

      // الحصول على حالة الإعداد
      const state = setupStates.get(interaction.user.id);

      if (!state) {
        const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
        return interaction.reply({
          content: errorMessage,
          flags: MessageFlags.Ephemeral
        });
      }

      // تحديث اللوحة في قاعدة البيانات - تم تعطيل مؤقتًا
      console.log('تم تعطيل panelService.updatePanel مؤقتًا');
      // await panelService.updatePanel(panelId, { ticketNameFormat: selectedFormat });

      // إرسال رسالة تأكيد
      const formatUpdatedMessage = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.format_updated');

      // الحصول على الترجمات
      const title = await client.translate(interaction.guild.id, 'panel_editor.title');
      const description = await client.translate(interaction.guild.id, 'panel_editor.description');
      const unsavedChanges = await client.translate(interaction.guild.id, 'panel_editor.unsaved_changes');
      const selectPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.select_placeholder');
      const backButtonLabel = await client.translate(interaction.guild.id, 'panel_editor.back_button');
      const saveButtonLabel = await client.translate(interaction.guild.id, 'panel_editor.save_button');

      // الحصول على جميع اللوحات للسيرفر - تم تعطيل مؤقتًا
      console.log('تم تعطيل panelService.getPanelsByGuild مؤقتًا');
      // إنشاء بيانات وهمية للوحات
      const panels = [
        { name: 'لوحة مؤقتة 1', panelId: 'panel1' },
        { name: 'لوحة مؤقتة 2', panelId: 'panel2' }
      ];

      // إنشاء الإمبد
      const embed = new EmbedBuilder()
        .setColor(client.config.embedColor)
        .setTitle(title)
        .setDescription(description)
        .setFooter({ text: unsavedChanges });

      // إنشاء قائمة منسدلة لاختيار اللوحة
      const selectOptions = panels.map(panel => ({
        label: panel.name,
        value: panel.panelId,
        default: panel.panelId === panelId
      }));

      const row1 = new ActionRowBuilder()
        .addComponents(
          new StringSelectMenuBuilder()
            .setCustomId('select_panel_to_edit')
            .setPlaceholder(selectPlaceholder)
            .addOptions(selectOptions)
        );

      const row2 = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('back_to_setup')
            .setLabel(backButtonLabel)
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('◀️'),
          new ButtonBuilder()
            .setCustomId('save_panel_changes')
            .setLabel(saveButtonLabel)
            .setStyle(ButtonStyle.Success)
        );

      // تحديث الرسالة
      await interaction.update({
        embeds: [embed],
        components: [row1, row2]
      });

      // إرسال رسالة تأكيد
      await interaction.followUp({
        content: formatUpdatedMessage,
        flags: MessageFlags.Ephemeral
      });
    } catch (error) {
      console.error('Error saving ticket name format:', error);
      const errorMessage = await client.translate(interaction.guild.id, 'errors.database_error');
      await interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }
  }
};
