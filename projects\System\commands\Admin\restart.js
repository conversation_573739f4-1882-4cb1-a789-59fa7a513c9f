const { EmbedBuilder } = require('discord.js');
const { DB } = require('pro.db-fork');
const { PermissionFlagsBits } = require('discord.js');
const { spawn } = require('child_process');

const db = new DB({ fileName: `restart.json` });

const commandPowers = require("../../Data/command-powers");
const commandName = "restart";
const owners = require("../../Data/owner");
module.exports = {
    name: "restart",
    async run(client, message, args) {
        try {
            let canHe = false;

            // التحقق من صلاحيات الأمر
            const doc = await commandPowers.findOne({ guildId: message.guild.id, name: commandName }).exec();
            if (!doc) {
                if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) {
                    return message.reply("> ⚠️ - لا يمكنك استخدام هذا الامر");
                }
                canHe = true;
            } else {
                const HasEnRole = message.member.roles.cache.some(role => doc.EnRoleIds.includes(role.id));
                const HasntEnRole = message.member.roles.cache.some(role => doc.DisRoleIds.includes(role.id));
                if (HasntEnRole) {
                    canHe = false;
                } else if (HasEnRole) {
                    canHe = true;
                } else {
                    if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) {
                        return message.reply("> ⚠️ - لا يمكنك استخدام هذا الامر");
                    }
                    canHe = true;
                }
            }

            // التحقق من المالكين
            const doc8 = await owners.findOne({ guildId: message.guild.id, userId: message.author.id }).exec();
            if (doc8) {
                canHe = true;
            }

            if (canHe === false) {
                return message.reply("> ⚠️ - لا يمكنك استخدام هذا الامر");
            }
            // إرسال رسالة تأكيد
            const embed = new EmbedBuilder()
                .setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
                .setTimestamp()
                .setFooter({ text: message.guild.name })
                .setColor("#2F3136")
                .setDescription(`> 🔄 - جاري إعادة تشغيل البوت...`);

            await message.channel.send({ embeds: [embed] });

            // حفظ معلومات القناة لإرسال رسالة بعد إعادة التشغيل
            db.set(message.guild.id, message.channel.id);

            // إعادة تشغيل البوت
            setTimeout(() => {
                const child = spawn(process.argv[0], process.argv.slice(1), {
                    detached: true,
                    stdio: 'ignore'
                });

                child.unref();
                process.exit(0);
            }, 1000); // انتظار ثانية واحدة قبل إعادة التشغيل

        } catch (error) {
            console.error('Error in restart command:', error);
            message.channel.send('> ❌ - حدث خطأ أثناء إعادة تشغيل البوت');
        }
    }
}