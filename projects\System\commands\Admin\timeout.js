const { EmbedBuilder } = require('discord.js')
const { PermissionFlagsBits } = require('discord.js');

const ms = require("ms")

const commandPowers = require("../../Data/command-powers");
const timeout = require('../../Data/timeout');
const owners = require("../../Data/owner")
const commandName = "timeout"

function isDurationString(str) {
    const regex = /^\d+(d|m|s|h|mo)$/i;
    return regex.test(str);
}

function getTimerEnd(expiration) {
    const day = expiration.toLocaleDateString();
    const hour = expiration.getHours();
    const minute = expiration.getMinutes();
    return { day, hour, minute };
}

module.exports = {
    name: "timeout",
    async run(client, message, args) {
        const doc = await commandPowers.findOne({ guildId: message.guild.id, name: commandName }).exec()
        if (!doc) {
            if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) return;
            canHe = true
        } else {
            const HasEnRole = message.member.roles.cache.some(role => doc.EnRoleIds.includes(role.id));
            const HasntEnRole = message.member.roles.cache.some(role => doc.DisRoleIds.includes(role.id));
            if (HasntEnRole) {
                canHe = false
            } else if (HasEnRole) {
                canHe = true
            } else {
                if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) return;
                canHe = true
            }
        }
      const doc8 = await owners.findOne({ guildId: message.guild.id, userId: message.author.id }).exec()
      if(doc8) {
        canHe = true
      }
        const member = message.mentions.members.first() || message.guild.members.cache.get(args[0])
        if (!member) return message.reply(`> :confused: - منشن الشخص`);
        var reason = ""
        var time = 0
        if (isDurationString(args[1]) === true) {
            time = ms(args[1])
            if (message.content.split(" ").slice(3).join(" ")) {
                reason = message.content.split(" ").slice(3).join(" ")
            } else {
                reason = null
            }
        } else {
            if (message.content.split(" ").slice(2).join(" ")) {
                reason = message.content.split(" ").slice(2).join(" ")
            } else {
                reason = null
            }
            time = null
        }
        const expiration = new Date(Date.now() + time);
        const timerEnd = getTimerEnd(expiration);
        const role1 = message.guild.roles.cache.find(ro => ro.name == 'Timeouted');
        const doc1 = await timeout.findOne({ userId: member.id }).exec()
        if (doc1) return message.reply(`> :confused: - تم اعطاء هذا الشخص تايم اوت بالفعل`);
        if (member.roles.cache.some(role => role.name === "Timeouted")) return message.reply(`> :confused: - تم اعطاء هذا الشخص تايم اوت بالفعل`);
        if (member.user.id === message.author.id) return message.reply(`> :joy: - لا يمكنك اعطاء تايم اوت لنفسك`);
        if (member.user.id === client.user.id) return message.reply(`> :face_with_symbols_over_mouth: - لا يمكنك اعطائي تايم اوت`);
        if (member.user.bot) return message.channel.send(`> :joy: - لا يمكنك اعطاء تايم اوت لبوت`);
        if (!role1) {
            await message.guild.roles.create({
                data: {
                    name: 'Timeouted'
                },
                reason: '**** Discord'
            })
                .then(async role => {
                    await role.setName('Timeouted')
                    await message.guild.channels.cache.forEach(c => {
                        c.permissionOverwrites.edit(role.id, { SendMessages: false });
                    })
                    member.roles.add(role)
                })
                .catch(console.error);
        } else {
            member.roles.add(role1)
        }
        if (isDurationString(args[1]) === true) {
            await member.timeout(time)
                .catch(console.log);
            const embed = new EmbedBuilder()
.setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
.setTimestamp()
.setFooter({ text: message.guild.name })
                .setDescription(`${message.author} تم اعطاء تايم اوت الى ${member} 👌`)
            message.channel.send({ embeds: [embed] })
            new timeout({
                guildId: message.guild.id,
                userId: member.id,
                reason: `${reason || "no reason"}`,
                giverId: message.author.id,
                duration: `${args[1] || "No specific time"}`,
                day: timerEnd.day,
                hour: timerEnd.hour,
                minute: timerEnd.minute,
                channelId: message.channel.id,
                date: new Date().toLocaleString()
            }).save()
        } else {
            await member.timeout(24 * 60 * 60 * 1000)
                .catch(console.log);
            const embed = new EmbedBuilder()
.setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
.setTimestamp()
.setFooter({ text: message.guild.name })
     .setDescription(`${message.author} تم اعطاء تايم اوت الى ${member} 👌`)
message.channel.send({ embeds: [embed] })
            new timeout({
                guildId: message.guild.id,
                userId: member.id,
                reason: `${reason || "no reason"}`,
                giverId: message.author.id,
                duration: "No specific time",
                day: timerEnd.day,
                hour: timerEnd.hour,
                minute: timerEnd.minute,
                channelId: message.channel.id,
                date: new Date().toLocaleString()
            }).save()
        }
        setTimeout(async () => {
            if (!member.roles.cache.some(role => role.name === "Timeouted")) return;
            member.roles.remove(message.guild.roles.cache.find(ro => ro.name == 'Timeouted'))
            await timeout.findOne({ guildId: message.guild.id, userId: member.id }).deleteOne().exec()
        }, time)
    }
}