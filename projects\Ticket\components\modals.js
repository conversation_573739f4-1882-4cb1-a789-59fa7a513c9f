const { MessageFlags } = require('discord.js');
const { setupStates } = require('./buttons');

// معالجة نموذج تعيين اسم اللوحة
async function handlePanelNameModal(interaction, client) {
  const state = setupStates.get(interaction.user.id);
  
  if (!state) {
    return interaction.reply({
      content: 'حدث خطأ في حالة الإعداد. يرجى بدء الإعداد مرة أخرى باستخدام الأمر `/setup`.',
      flags: MessageFlags.Ephemeral
    });
  }
  
  // الحصول على اسم اللوحة من النموذج
  const panelName = interaction.fields.getTextInputValue('panel_name_input');
  
  // تحديث اسم اللوحة في حالة الإعداد
  state.panelName = panelName;
  setupStates.set(interaction.user.id, state);
  
  // إعادة عرض الخطوة الحالية مع الاسم المحدث
  const { showStep1 } = require('../commands/setup');
  await showStep1(interaction, client, state);
}

// تصدير معالجات النماذج
module.exports = {
  handlePanelNameModal
};
