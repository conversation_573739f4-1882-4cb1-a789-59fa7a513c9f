const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle, StringSelectMenuBuilder, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'select_channel_for_panel',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      try {
        if (!interaction.replied && !interaction.deferred) {
          return interaction.reply({
            content: errorMessage,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (error) {
        console.error('Error replying to interaction in state check:', error);
      }
      return;
    }

    // تحديث قناة الهدف المحددة
    state.targetChannel = interaction.values[0];
    setupStates.set(interaction.user.id, state);

    // الحصول على اللوحة المحددة
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      try {
        if (!interaction.replied && !interaction.deferred) {
          return interaction.reply({
            content: errorMessage,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (error) {
        console.error('Error replying to interaction in panel check:', error);
      }
      return;
    }

    // الحصول على القناة المحددة
    const targetChannel = interaction.guild.channels.cache.get(state.targetChannel);

    if (!targetChannel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.channel_not_found');
      try {
        if (!interaction.replied && !interaction.deferred) {
          return interaction.reply({
            content: errorMessage,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (error) {
        console.error('Error replying to interaction in channel check:', error);
      }
      return;
    }

    try {
      // الحصول على الترجمات
      const ticketDescription = await client.translate(interaction.guild.id, 'ticket.create_button');

      // إنشاء مكونات اللوحة حسب نوع العرض
      let row;

      // نوع العرض
      const displayType = selectedPanel.displayType || 'dropdown_message';
      console.log('نوع العرض المحدد:', displayType);

      if (displayType.startsWith('dropdown')) {
        // إنشاء قائمة منسدلة
        const options = [];

        if (selectedPanel.categories && selectedPanel.categories.length > 0) {
          // إضافة خيارات للفئات
          for (const category of selectedPanel.categories) {
            // إنشاء خيار جديد بدون رموز تعبيرية
            options.push({
              label: category.name,
              description: category.description || 'إنشاء تذكرة دعم',
              value: `ticket_category_${selectedPanel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`
            });
          }
        } else {
          // إضافة خيار افتراضي إذا لم تكن هناك فئات
          options.push({
            label: ticketDescription,
            description: 'إنشاء تذكرة دعم جديدة',
            value: `create_ticket_${selectedPanel.panelId}`
          });
        }

        row = new ActionRowBuilder()
          .addComponents(
            new StringSelectMenuBuilder()
              .setCustomId(`create_ticket_${selectedPanel.panelId}`)
              .setPlaceholder(ticketDescription)
              .addOptions(options)
          );
      } else {
        // إنشاء أزرار
        const buttons = [];

        if (selectedPanel.categories && selectedPanel.categories.length > 0) {
          // إضافة أزرار للفئات
          for (const category of selectedPanel.categories) {
            if (buttons.length < 5) { // الحد الأقصى هو 5 أزرار في صف واحد
              // إنشاء زر جديد بدون رموز تعبيرية
              buttons.push(
                new ButtonBuilder()
                  .setCustomId(`create_ticket_${selectedPanel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`)
                  .setLabel(category.name)
                  .setStyle(ButtonStyle.Primary)
              );
            }
          }
        } else {
          // إضافة زر افتراضي إذا لم تكن هناك فئات
          buttons.push(
            new ButtonBuilder()
              .setCustomId(`create_ticket_${selectedPanel.panelId}`)
              .setLabel(ticketDescription)
              .setStyle(ButtonStyle.Primary)
          );
        }

        row = new ActionRowBuilder().addComponents(buttons);
      }

      // إرسال اللوحة إلى القناة المحددة
      let sentMessage;

      if ((displayType === 'dropdown_image' || displayType === 'buttons_image') && selectedPanel.imageUrl) {
        // إرسال صورة مباشرة بدون إمبد
        sentMessage = await targetChannel.send({
          files: [selectedPanel.imageUrl],
          components: [row]
        });
      } else {
        // إرسال إمبد مع وصف
        const panelEmbed = new EmbedBuilder()
          .setColor(client.config.embedColor)
          .setTitle(`${selectedPanel.name}`)
          .setDescription(selectedPanel.description || 'انقر على الزر أدناه لإنشاء تذكرة دعم.')
          .setFooter({ text: client.config.footerText });

        sentMessage = await targetChannel.send({
          embeds: [panelEmbed],
          components: [row]
        });
      }

      // تحديث معرف الرسالة والقناة في قاعدة البيانات
      await panelService.updatePanelMessage(selectedPanel.panelId, sentMessage.id, targetChannel.id);

      // الحصول على الترجمات
      const title = await client.translate(interaction.guild.id, 'panel_editor.title');
      const description = await client.translate(interaction.guild.id, 'panel_editor.description');
      const unsavedChanges = await client.translate(interaction.guild.id, 'panel_editor.unsaved_changes');
      const panelSentMessage = await client.translate(interaction.guild.id, 'panel_editor.panel_sent', { channel: targetChannel.toString() });

      // الحصول على جميع اللوحات
      const panels = await panelService.getPanelsByGuild(interaction.guild.id);

      // إعادة عرض واجهة محرر اللوحات
      const embed = new EmbedBuilder()
        .setColor(client.config.embedColor)
        .setTitle(title)
        .setDescription(description)
        .addFields(
          { name: '\\u200B', value: unsavedChanges }
        );

      const row1 = new ActionRowBuilder()
        .addComponents(
          new StringSelectMenuBuilder()
            .setCustomId('select_panel_to_edit')
            .setPlaceholder(`[${selectedPanel.panelId.substring(0, 8)}...] ${selectedPanel.name}`)
            .addOptions(panels.map(panel => ({
              label: panel.name,
              value: panel.panelId,
              description: `Panel ID: ${panel.panelId.substring(0, 16)}...`
            })))
        );

      // الحصول على ترجمات الإجراءات
      const actionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.action_placeholder');
      const sendPanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.label');
      const sendPanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.description');
      const editNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.label');
      const editNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.description');
      const editDescriptionLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.label');
      const editDescriptionDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.description');
      const editWelcomeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.label');
      const editWelcomeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.description');
      const editDisplayTypeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.label');
      const editDisplayTypeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.description');
      const addCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.label');
      const addCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.description');
      const editRolesLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.label');
      const editRolesDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.description');
      const editTranscriptLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.label');
      const editTranscriptDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.description');
      const editCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.label');
      const editCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.description');
      const deletePanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.label');
      const deletePanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.description');

      const row2 = new ActionRowBuilder()
        .addComponents(
          new StringSelectMenuBuilder()
            .setCustomId('select_panel_action')
            .setPlaceholder(actionPlaceholder)
            .addOptions([
              {
                label: sendPanelLabel,
                value: 'send_panel',
                description: sendPanelDesc,
                emoji: '0️⃣'
              },
              {
                label: editNameLabel,
                value: 'edit_panel_name',
                description: editNameDesc,
                emoji: '1️⃣'
              },
              {
                label: editDescriptionLabel,
                value: 'edit_panel_description',
                description: editDescriptionDesc,
                emoji: '2️⃣'
              },
              {
                label: editWelcomeLabel,
                value: 'edit_welcome_message',
                description: editWelcomeDesc,
                emoji: '3️⃣'
              },
              {
                label: editDisplayTypeLabel,
                value: 'edit_display_type',
                description: editDisplayTypeDesc,
                emoji: '4️⃣'
              },
              {
                label: addCategoryLabel,
                value: 'add_category',
                description: addCategoryDesc,
                emoji: '5️⃣'
              },
              {
                label: editRolesLabel,
                value: 'edit_support_roles',
                description: editRolesDesc,
                emoji: '6️⃣'
              },
              {
                label: editTranscriptLabel,
                value: 'edit_transcript_channel',
                description: editTranscriptDesc,
                emoji: '7️⃣'
              },
              {
                label: editCategoryLabel,
                value: 'edit_ticket_category',
                description: editCategoryDesc,
                emoji: '8️⃣'
              },
              {
                label: deletePanelLabel,
                value: 'delete_panel',
                description: deletePanelDesc,
                emoji: '9️⃣'
              }
            ])
        );

      // الحصول على ترجمات الأزرار
      const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
      const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

      const row3 = new ActionRowBuilder()
        .addComponents(
          new ButtonBuilder()
            .setCustomId('setup_back')
            .setLabel(backButton)
            .setStyle(ButtonStyle.Secondary)
            .setEmoji('◀️'),
          new ButtonBuilder()
            .setCustomId('save_panel_changes')
            .setLabel(saveButton)
            .setStyle(ButtonStyle.Secondary)
        );

      // تحديث الرسالة مع رسالة التأكيد
      try {
        // التحقق من حالة التفاعل قبل المحاولة
        if (!interaction.replied && !interaction.deferred) {
          await interaction.update({
            content: panelSentMessage,
            embeds: [embed],
            components: [row1, row2, row3]
          });
        } else if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({
            content: panelSentMessage,
            embeds: [embed],
            components: [row1, row2, row3]
          });
        } else {
          // إذا تم الرد بالفعل، استخدم followUp
          await interaction.followUp({
            content: panelSentMessage,
            embeds: [embed],
            components: [row1, row2, row3],
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (updateError) {
        console.error('Error updating interaction:', updateError);
        // محاولة أخيرة للرد إذا فشل التحديث
        try {
          if (!interaction.replied) {
            await interaction.reply({
              content: panelSentMessage,
              embeds: [embed],
              components: [row1, row2, row3],
              flags: MessageFlags.Ephemeral
            });
          }
        } catch (fallbackError) {
          console.error('Error in fallback reply:', fallbackError);
        }
      }
    } catch (error) {
      console.error('Error sending panel to channel:', error);
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_creation_error');

      try {
        if (!interaction.replied && !interaction.deferred) {
          await interaction.reply({
            content: errorMessage,
            flags: MessageFlags.Ephemeral
          });
        } else if (interaction.deferred && !interaction.replied) {
          await interaction.editReply({
            content: errorMessage
          });
        } else {
          await interaction.followUp({
            content: errorMessage,
            flags: MessageFlags.Ephemeral
          });
        }
      } catch (replyError) {
        console.error('Error replying to interaction in error handler:', replyError);
      }
    }
  }
};
