const { EmbedBuilder , ChannelType } = require('discord.js')
const { PermissionFlagsBits} = require('discord.js');

const commandPowers = require("../../Data/command-powers")

const commandName = "setticket"
const owners = require("../../Data/owner")
const Database = require("../../Data/Tickets")
module.exports = {
    name: "setticket",
    async run(client, message, args) {
        const doc = await commandPowers.findOne({ guildId: message.guild.id, name: commandName }).exec()
        if (!doc) {
            if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) return;
            canHe = true
        } else {
            const HasEnRole = message.member.roles.cache.some(role => doc.EnRoleIds.includes(role.id));
            const HasntEnRole = message.member.roles.cache.some(role => doc.DisRoleIds.includes(role.id));
            if (HasntEnRole) {
                canHe = false
            } else if (HasEnRole) {
                canHe = true
            } else {
                if (!message.member.permissions.has(PermissionFlagsBits.Administrator)) return;
                canHe = true
            }
        }
        const doc8 = await owners.findOne({ guildId: message.guild.id, userId: message.author.id }).exec()
        if (doc8) {
            canHe = true
        }
        if (canHe === false) return message.reply("> ⚠️ - لا يمكنك استخدام هذا الامر")
        const Data = await Database.findOne({guildId : message.guild.id}) || await Database.create({guildId : message.guild.id})
        if(!args[0]) {
            if (!Data.OpenCatagory) {
                message.guild.channels.create({
                    name: "Ticket",
                    type: ChannelType.GuildCategory,
                }).then(async(cat)=>{
                    Data.OpenCatagory = cat.id
                    await Data.save().then(()=>{
                        message.guild.channels.create({
                            name: "log-ticket",
                            type: ChannelType.GuildText,
                            parent : cat.id,
                            permissionOverwrites: [
                                {
                                  id: message.guild.roles.everyone,
                                  deny: [PermissionFlagsBits.ViewChannel]
                                },
                                {
                                    id: client.user,
                                    allow: [PermissionFlagsBits.ViewChannel ,PermissionFlagsBits.SendMessages,PermissionFlagsBits.AttachFiles,PermissionFlagsBits.ReadMessageHistory]
                                }
                             ],
                        }).then(async(cha)=>{
                            Data.Log = cha.id
                           await Data.save()
                        })
                        const Embed = new EmbedBuilder()
                            .setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
                            .setTimestamp()
                            .setFooter({ text: message.guild.name })
                            .setDescription(`> ✅ تم تفعيل النظام بنجاح`)
                        message.reply({ embeds: [Embed] })

                    })
                })
            } else {
                const Embed = new EmbedBuilder()
                .setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
                .setTimestamp()
                .setFooter({ text: message.guild.name })
                .setDescription(`> **تم تفعيل نظام التذاكر بالفعل**

                > **المتغيرات:**

                > setticket \`\`msg , img , role\`\``)
            message.reply({ embeds: [Embed] })

            }
        }

if(args[0] ==="img") {
    let img = message.attachments.first()
    if(!img) return message.reply({content : "> :confused: - ارفع صورة"})

    Data.Img = img.url
    await Data.save().then(()=>{
        const Embed = new EmbedBuilder()
        .setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
        .setTimestamp()
        .setFooter({ text: message.guild.name })
        .setDescription(`> ✅ تم تغيير الصوره بنجاح `)
    message.reply({ embeds: [Embed] })

    })
}

if (args[0] ==="msg") {
  let Mesg =  message.content.split(' ').slice('2').join(' ')
  if(!Mesg) return message.channel.send({content : "> :confused: - ارسل الرساله "})
  Data.Msg = Mesg
  await Data.save().then(()=>{
      const Embed = new EmbedBuilder()
      .setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
      .setTimestamp()
      .setFooter({ text: message.guild.name })
      .setDescription(`> ✅ تم تغيير الرساله بنجاح`)
  message.channel.send({ embeds: [Embed] })

  })

}

if(args[0] ==="role") {
    const Role = message.mentions.roles.first();
    if(!Role) return message.reply({content : "> :confused: - حدد الرول "})
    if(Data.Role.includes(Role.id)) {
       const Pull = await Database.findOneAndUpdate({guildId : message.guild.id } , { $pull: { 'Role': Role.id } }).then(()=>{
        const Embed = new EmbedBuilder()
        .setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
        .setTimestamp()
        .setFooter({ text: message.guild.name })
        .setDescription(`> تم ازاله الرول بنجاح`)
    message.reply({ embeds: [Embed] })
       })
    }else{
        const Pull = await Database.findOneAndUpdate({guildId : message.guild.id } , { $push: { 'Role': Role.id } }).then(()=>{
            const Embed = new EmbedBuilder()
            .setAuthor({ name: message.author.username, iconURL: message.author.avatarURL(), url: message.author.avatarURL() })
            .setTimestamp()
            .setFooter({ text: message.guild.name })
            .setDescription(`> تم اضافه الرول بنجاح`)
        message.reply({ embeds: [Embed] })

        })

    }
}


    }
}