const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔧 تثبيت مكتبات الصوت المطلوبة...');

// قائمة المكتبات المطلوبة
const requiredPackages = [
  '@discordjs/voice@^0.16.1',
  '@discordjs/opus@^0.9.0',
  'ffmpeg-static@^5.2.0',
  'sodium-native@^4.0.4',
  'prism-media@^1.3.5',
  'opusscript@^0.0.8'
];

// دالة تثبيت المكتبات
function installPackages() {
  return new Promise((resolve, reject) => {
    console.log('📦 تثبيت المكتبات...');
    
    const npm = spawn('npm', ['install', ...requiredPackages], {
      stdio: 'inherit',
      shell: true,
      cwd: __dirname
    });
    
    npm.on('close', (code) => {
      if (code === 0) {
        console.log('✅ تم تثبيت جميع المكتبات بنجاح!');
        resolve();
      } else {
        console.error('❌ فشل في تثبيت المكتبات');
        reject(new Error(`npm install failed with code ${code}`));
      }
    });
    
    npm.on('error', (error) => {
      console.error('❌ خطأ في تثبيت المكتبات:', error);
      reject(error);
    });
  });
}

// دالة فحص المكتبات
function checkPackages() {
  console.log('🔍 فحص المكتبات المثبتة...');
  
  const results = {};
  
  for (const pkg of requiredPackages) {
    const packageName = pkg.split('@')[0];
    try {
      require(packageName);
      results[packageName] = '✅ متوفر';
    } catch (error) {
      results[packageName] = '❌ مفقود';
    }
  }
  
  console.log('\n📊 نتائج الفحص:');
  for (const [pkg, status] of Object.entries(results)) {
    console.log(`   ${pkg}: ${status}`);
  }
  
  const allInstalled = Object.values(results).every(status => status.includes('✅'));
  
  if (allInstalled) {
    console.log('\n🎉 جميع المكتبات متوفرة!');
  } else {
    console.log('\n⚠️ بعض المكتبات مفقودة');
  }
  
  return allInstalled;
}

// دالة إنشاء تقرير تشخيص
function generateDiagnosticReport() {
  console.log('\n📋 إنشاء تقرير تشخيص...');
  
  const report = {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    platform: process.platform,
    arch: process.arch,
    packages: {},
    recommendations: []
  };
  
  // فحص المكتبات
  for (const pkg of requiredPackages) {
    const packageName = pkg.split('@')[0];
    try {
      const packagePath = require.resolve(packageName);
      const packageJson = require(path.join(packagePath, '../package.json'));
      report.packages[packageName] = {
        installed: true,
        version: packageJson.version,
        path: packagePath
      };
    } catch (error) {
      report.packages[packageName] = {
        installed: false,
        error: error.message
      };
      report.recommendations.push(`تثبيت ${packageName}`);
    }
  }
  
  // فحص ffmpeg
  try {
    const ffmpegStatic = require('ffmpeg-static');
    if (ffmpegStatic && fs.existsSync(ffmpegStatic)) {
      report.ffmpeg = {
        available: true,
        path: ffmpegStatic
      };
    } else {
      report.ffmpeg = {
        available: false,
        error: 'ffmpeg-static path not found'
      };
      report.recommendations.push('إعادة تثبيت ffmpeg-static');
    }
  } catch (error) {
    report.ffmpeg = {
      available: false,
      error: error.message
    };
    report.recommendations.push('تثبيت ffmpeg-static');
  }
  
  // حفظ التقرير
  const reportPath = path.join(__dirname, 'audio-diagnostic-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`📄 تم حفظ التقرير في: ${reportPath}`);
  
  return report;
}

// دالة طباعة التقرير
function printReport(report) {
  console.log('\n📊 تقرير التشخيص:');
  console.log('='.repeat(50));
  console.log(`🕒 الوقت: ${report.timestamp}`);
  console.log(`🖥️ النظام: ${report.platform} ${report.arch}`);
  console.log(`📦 Node.js: ${report.nodeVersion}`);
  
  console.log('\n📚 المكتبات:');
  for (const [pkg, info] of Object.entries(report.packages)) {
    if (info.installed) {
      console.log(`   ✅ ${pkg} v${info.version}`);
    } else {
      console.log(`   ❌ ${pkg} - ${info.error}`);
    }
  }
  
  console.log('\n🎬 FFmpeg:');
  if (report.ffmpeg.available) {
    console.log(`   ✅ متوفر في: ${report.ffmpeg.path}`);
  } else {
    console.log(`   ❌ غير متوفر - ${report.ffmpeg.error}`);
  }
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 التوصيات:');
    report.recommendations.forEach((rec, index) => {
      console.log(`   ${index + 1}. ${rec}`);
    });
  }
  
  console.log('='.repeat(50));
}

// دالة إصلاح المشاكل
async function fixIssues() {
  console.log('\n🔧 محاولة إصلاح المشاكل...');
  
  try {
    // تثبيت المكتبات المفقودة
    await installPackages();
    
    // فحص مرة أخرى
    const allInstalled = checkPackages();
    
    if (allInstalled) {
      console.log('\n🎉 تم إصلاح جميع المشاكل!');
      return true;
    } else {
      console.log('\n⚠️ لا تزال هناك مشاكل');
      return false;
    }
    
  } catch (error) {
    console.error('\n❌ فشل في إصلاح المشاكل:', error.message);
    return false;
  }
}

// التشغيل الرئيسي
async function main() {
  console.log('🎵 مساعد تشخيص وإصلاح مكتبات الصوت');
  console.log('='.repeat(50));
  
  try {
    // فحص أولي
    const initialCheck = checkPackages();
    
    if (!initialCheck) {
      console.log('\n🔧 محاولة إصلاح المشاكل...');
      const fixed = await fixIssues();
      
      if (!fixed) {
        console.log('\n❌ لم يتم إصلاح جميع المشاكل');
        console.log('💡 جرب تشغيل الأوامر التالية يدوياً:');
        console.log(`   npm install ${requiredPackages.join(' ')}`);
      }
    }
    
    // إنشاء تقرير نهائي
    const report = generateDiagnosticReport();
    printReport(report);
    
    console.log('\n✨ انتهى التشخيص والإصلاح');
    
  } catch (error) {
    console.error('❌ خطأ في التشغيل:', error);
    process.exit(1);
  }
}

// تشغيل إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  main();
}

module.exports = {
  installPackages,
  checkPackages,
  generateDiagnosticReport,
  fixIssues
};
