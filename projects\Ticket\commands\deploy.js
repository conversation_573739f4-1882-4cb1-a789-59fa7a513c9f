const { SlashCommandBuilder, PermissionFlagsBits, MessageFlags } = require('discord.js');
const { checkBotPermissions, handleDiscordAPIError, retryWithDelay } = require('../utils/permissionChecker');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('deploy')
    .setDescription('Deploy slash commands to this server')
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction, client) {
    try {
      // التحقق من الصلاحيات
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return await interaction.reply({
          content: '❌ تحتاج إلى صلاحية المدير لاستخدام هذا الأمر.',
          flags: MessageFlags.Ephemeral
        });
      }

      // فحص صلاحيات البوت
      const permissionCheck = await checkBotPermissions(client, interaction.guild);
      if (!permissionCheck.hasPermissions) {
        return await interaction.reply({
          content: '❌ البوت لا يملك الصلاحيات الكافية لتطبيق الأوامر. يحتاج البوت إلى صلاحية Administrator.',
          flags: MessageFlags.Ephemeral
        });
      }

      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      // استيراد دالة تطبيق السلاش كوماند
      const { REST, Routes } = require('discord.js');

      // جمع جميع الأوامر
      const commands = [];
      client.commands.forEach(command => {
        if (command.data) {
          commands.push(command.data.toJSON());
        }
      });

      if (commands.length === 0) {
        return await interaction.editReply({
          content: '❌ لم يتم العثور على أوامر لتطبيقها.'
        });
      }

      // إنشاء REST client
      const rest = new REST({ version: '10' }).setToken(client.token);

      console.log(`Started refreshing ${commands.length} application (/) commands for guild ${interaction.guild.id}.`);

      // تطبيق الأوامر للسيرفر الحالي مع إعادة المحاولة
      const data = await retryWithDelay(async () => {
        return await rest.put(
          Routes.applicationGuildCommands(client.user.id, interaction.guild.id),
          { body: commands }
        );
      }, 3, 2000).catch(error => {
        console.error('Error deploying commands:', error);
        const errorMessage = handleDiscordAPIError(error);
        throw new Error(errorMessage);
      });

      console.log(`Successfully reloaded ${data.length} application (/) commands for guild ${interaction.guild.id}.`);

      await interaction.editReply({
        content: `✅ تم تطبيق ${data.length} أمر سلاش بنجاح في هذا السيرفر!\n\n📋 الأوامر المطبقة:\n${commands.map(cmd => `• \`/${cmd.name}\``).join('\n')}`
      });

    } catch (error) {
      console.error('Error in deploy command:', error);

      const errorMessage = error.message.includes('Missing Permissions')
        ? '❌ البوت لا يملك الصلاحيات اللازمة لتطبيق الأوامر.'
        : '❌ حدث خطأ أثناء تطبيق الأوامر.';

      if (interaction.deferred) {
        await interaction.editReply({ content: errorMessage });
      } else {
        await interaction.reply({ content: errorMessage, flags: MessageFlags.Ephemeral });
      }
    }
  },
};
