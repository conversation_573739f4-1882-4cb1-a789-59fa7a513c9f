const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRowBuilder, <PERSON>tonBuilder, ButtonStyle, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'select_category_to_edit',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // تحديث الفئة المحددة
    state.selectedCategoryIndex = parseInt(interaction.values[0]);
    setupStates.set(interaction.user.id, state);

    // الحصول على معلومات اللوحة من قاعدة البيانات
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      return interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // الحصول على الفئة المحددة
    const selectedCategory = selectedPanel.categories[state.selectedCategoryIndex];

    if (!selectedCategory) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.category_not_found');
      return interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // الحصول على الترجمات
    const modalTitle = await client.translate(interaction.guild.id, 'panel_editor.edit_category_modal.title');
    const nameLabel = await client.translate(interaction.guild.id, 'panel_editor.edit_category_modal.name_label');
    const namePlaceholder = await client.translate(interaction.guild.id, 'panel_editor.edit_category_modal.name_placeholder');
    const descriptionLabel = await client.translate(interaction.guild.id, 'panel_editor.edit_category_modal.description_label');
    const descriptionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.edit_category_modal.description_placeholder');
    const emojiLabel = await client.translate(interaction.guild.id, 'panel_editor.edit_category_modal.emoji_label');
    const emojiPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.edit_category_modal.emoji_placeholder');

    // إنشاء نموذج لتعديل الفئة
    const modal = new ModalBuilder()
      .setCustomId('edit_category_modal')
      .setTitle(modalTitle);

    const nameInput = new TextInputBuilder()
      .setCustomId('category_name_input')
      .setLabel(nameLabel)
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(namePlaceholder)
      .setValue(selectedCategory.name)
      .setRequired(true);

    const descriptionInput = new TextInputBuilder()
      .setCustomId('category_description_input')
      .setLabel(descriptionLabel)
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(descriptionPlaceholder)
      .setValue(selectedCategory.description || '')
      .setRequired(true);

    const emojiInput = new TextInputBuilder()
      .setCustomId('category_emoji_input')
      .setLabel(emojiLabel)
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(emojiPlaceholder)
      .setValue(selectedCategory.emoji || '')
      .setRequired(false);

    const nameRow = new ActionRowBuilder().addComponents(nameInput);
    const descriptionRow = new ActionRowBuilder().addComponents(descriptionInput);
    const emojiRow = new ActionRowBuilder().addComponents(emojiInput);

    modal.addComponents(nameRow, descriptionRow, emojiRow);

    await interaction.showModal(modal);
  }
};
