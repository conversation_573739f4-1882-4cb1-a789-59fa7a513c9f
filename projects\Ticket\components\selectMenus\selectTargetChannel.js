const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, ButtonStyle, StringSelectMenuBuilder, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');

module.exports = {
  id: 'select_target_channel',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // تحديث قناة الهدف المحددة
    state.targetChannel = interaction.values[0];
    setupStates.set(interaction.user.id, state);

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'setup.steps.step5.title');
    const description = await client.translate(interaction.guild.id, 'setup.steps.step5.description');

    // إعادة عرض الخطوة الحالية مع القيم المحدثة
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description);

    // الحصول على جميع القنوات النصية في السيرفر
    const textChannels = interaction.guild.channels.cache
      .filter(channel => channel.type === 0) // TextChannel
      .map(channel => ({
        label: channel.name,
        value: channel.id,
        description: `Channel ID: ${channel.id}`
      }));

    // عرض القناة المحددة
    const selectedChannel = interaction.guild.channels.cache.get(state.targetChannel);

    // الحصول على ترجمات إضافية
    const selectPlaceholder = await client.translate(interaction.guild.id, 'setup.steps.step5.select_placeholder');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const finishButton = await client.translate(interaction.guild.id, 'setup.finish_button');

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_target_channel')
          .setPlaceholder(selectedChannel ? selectedChannel.name : selectPlaceholder)
          .addOptions(textChannels.slice(0, 25))
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('setup_finish')
          .setLabel(finishButton)
          .setStyle(ButtonStyle.Success)
      );

    await interaction.update({ embeds: [embed], components: [row1, row2] });
  }
};
