
const { PermissionFlagsBits , <PERSON><PERSON><PERSON><PERSON>er, ActionRowBuilder , EmbedBuilder , ButtonStyle, MessageFlags } = require('discord.js');
const moment = require('moment');
const Database = require("../../../Data/Tickets Data")
const Database2 = require("../../../Data/Tickets")

module.exports = {
    name: "close-ticket",
    run: async(client, interaction) => {
const Data = await Database.findOne({guildid :interaction.guild.id , channelId : interaction.channel.id })
const Data2 = await Database2.findOne({guildId : interaction.guild.id })

if(Data.statue === "Closed") return interaction.reply({ content: `⚠️ • التكت مغلق بالفعل`, flags: MessageFlags.Ephemeral });

const Channel = interaction.guild.channels.cache.get(Data2.Log);
if (!Channel) return
const Embedsr = new EmbedBuilder()
.setTitle("اغلاق")
.setColor("NotQuiteBlack")
.setDescription(`- \`\`${interaction.channel.name}\`\` : اسم التذكره \n - المسؤل عن الاغلاق : ${interaction.user}` )
.setTimestamp()
Channel.send({embeds : [Embedsr]})



const row = new ActionRowBuilder()
.addComponents(
  new ButtonBuilder()
    .setCustomId('reopen')
    .setLabel('🔓 اعادة الفتح')
    .setStyle(ButtonStyle.Secondary),
).addComponents(  new ButtonBuilder()
.setCustomId('delete')
.setLabel('❎ حذف التذكره')
.setStyle(ButtonStyle.Secondary),
)


const embed0 = new EmbedBuilder()
    
.setColor("Yellow")
.setDescription(`اغلقت بواسطه : ${interaction.user}`)

interaction.reply({embeds : [embed0] });

let permissionOverwrites1 = [];

if (Data2.Role[0]) {
    const roleIds = Data2.Role

    permissionOverwrites1 = roleIds.map(roleId => ({
      id: roleId,
      allow: [
        PermissionFlagsBits.ViewChannel,
        PermissionFlagsBits.SendMessages,
        PermissionFlagsBits.AttachFiles,
        PermissionFlagsBits.ReadMessageHistory,
      ]
    }));
  }

interaction.channel.edit({
    permissionOverwrites: [
        {
          id: interaction.guild.roles.everyone, 
          deny: [PermissionFlagsBits.ViewChannel]
        },
        {
            id: client.user, 
            allow: [PermissionFlagsBits.ViewChannel ,PermissionFlagsBits.SendMessages,PermissionFlagsBits.AttachFiles,PermissionFlagsBits.ReadMessageHistory]
        },
        ...permissionOverwrites1
     ],
     }).then(async(ch)=>{
        const embed = new EmbedBuilder()
        .setColor("Red")
        .setDescription('```fix\nاعدادات  فريق الدعم ```')
        ch.send({embeds : [embed] , components : [row]})
        Data.statue = "Closed"
       await Data.save()
     })
    }
};