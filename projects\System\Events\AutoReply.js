const Database = require("../Data/AutoReply");

module.exports = {
  name: 'messageCreate',
  run: async (message, client) => {
    if (message.author.bot) return;

    if(!message.content.length > 0 ) return

    try {
      const result = await Database.findOne({guild : message.guild.id, Message: { $regex: message.content, $options: "i" }  });
      if(!result) return

      if(result) {
        // استخدام send بدلاً من reply لتجنب مشاكل message_reference
        await message.channel.send({
          content: result.Reply,
          allowedMentions: { repliedUser: false }
        });
      }
    } catch (error) {
      console.error('Error in AutoReply:', error);
    }
  }
};
