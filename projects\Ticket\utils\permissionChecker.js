const { PermissionsBitField } = require('discord.js');

/**
 * فحص صلاحيات البوت قبل تطبيق الأوامر
 * @param {Client} client - عميل Discord
 * @param {Guild} guild - السيرفر
 * @returns {Object} نتيجة الفحص
 */
async function checkBotPermissions(client, guild) {
  try {
    const botMember = await guild.members.fetch(client.user.id);
    
    // الصلاحيات المطلوبة لتطبيق الأوامر
    const requiredPermissions = [
      PermissionsBitField.Flags.ManageGuild,
      PermissionsBitField.Flags.Administrator
    ];

    const missingPermissions = [];
    
    for (const permission of requiredPermissions) {
      if (!botMember.permissions.has(permission)) {
        missingPermissions.push(permission);
      }
    }

    return {
      hasPermissions: missingPermissions.length === 0,
      missingPermissions,
      botMember
    };
  } catch (error) {
    console.error('Error checking bot permissions:', error);
    return {
      hasPermissions: false,
      missingPermissions: [],
      error: error.message
    };
  }
}

/**
 * فحص صلاحيات المستخدم
 * @param {GuildMember} member - عضو السيرفر
 * @param {Array} requiredPermissions - الصلاحيات المطلوبة
 * @returns {Boolean} هل لديه الصلاحيات
 */
function checkUserPermissions(member, requiredPermissions = [PermissionsBitField.Flags.Administrator]) {
  return requiredPermissions.some(permission => member.permissions.has(permission));
}

/**
 * التحقق من وجود الدور في السيرفر
 * @param {Guild} guild - السيرفر
 * @param {String} roleId - معرف الدور
 * @returns {Boolean} هل الدور موجود
 */
function checkRoleExists(guild, roleId) {
  try {
    const role = guild.roles.cache.get(roleId);
    return !!role;
  } catch (error) {
    console.warn(`Role with ID ${roleId} not found in guild cache, skipping permission overwrite.`);
    return false;
  }
}

/**
 * إنشاء أذونات القناة مع التحقق من وجود الأدوار
 * @param {Guild} guild - السيرفر
 * @param {Array} roleIds - معرفات الأدوار
 * @param {Array} permissions - الصلاحيات المطلوبة
 * @returns {Array} قائمة الأذونات الصالحة
 */
function createSafePermissionOverwrites(guild, roleIds, permissions) {
  const validOverwrites = [];
  
  for (const roleId of roleIds) {
    if (checkRoleExists(guild, roleId)) {
      validOverwrites.push({
        id: roleId,
        allow: permissions
      });
    }
  }
  
  return validOverwrites;
}

/**
 * معالجة أخطاء Discord API
 * @param {Error} error - الخطأ
 * @returns {String} رسالة خطأ مفهومة
 */
function handleDiscordAPIError(error) {
  const errorMessages = {
    50001: 'البوت لا يملك الصلاحيات الكافية لتنفيذ هذا الإجراء',
    50013: 'البوت لا يملك الصلاحيات الكافية',
    50035: 'البيانات المرسلة غير صالحة',
    40060: 'التفاعل منتهي الصلاحية',
    10062: 'التفاعل غير معروف',
    10008: 'الرسالة غير معروفة',
    10003: 'القناة غير معروفة'
  };

  const code = error.code || error.status;
  return errorMessages[code] || `خطأ غير معروف: ${error.message}`;
}

/**
 * إعادة المحاولة مع تأخير
 * @param {Function} fn - الدالة المراد تنفيذها
 * @param {Number} retries - عدد المحاولات
 * @param {Number} delay - التأخير بالميلي ثانية
 * @returns {Promise} نتيجة التنفيذ
 */
async function retryWithDelay(fn, retries = 3, delay = 1000) {
  for (let i = 0; i < retries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === retries - 1) throw error;
      
      console.warn(`Attempt ${i + 1} failed, retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
      delay *= 2; // زيادة التأخير مع كل محاولة
    }
  }
}

module.exports = {
  checkBotPermissions,
  checkUserPermissions,
  checkRoleExists,
  createSafePermissionOverwrites,
  handleDiscordAPIError,
  retryWithDelay
};
