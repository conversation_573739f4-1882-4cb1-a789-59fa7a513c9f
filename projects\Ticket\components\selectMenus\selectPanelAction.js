const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle, StringSelectMenuBuilder, ModalBuilder, TextInputBuilder, TextInputStyle, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'select_panel_action',
  // إضافة معرف إضافي للقائمة المنسدلة الإضافية
  extraId: 'select_panel_action_extra',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      if (!interaction.replied && !interaction.deferred) {
        return interaction.reply({
          content: errorMessage,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // تحديث الإجراء المحدد
    state.selectedAction = interaction.values[0];
    setupStates.set(interaction.user.id, state);

    // الحصول على معلومات اللوحة من قاعدة البيانات
    const selectedPanel = await panelService.getPanelById(state.selectedPanel);

    // الحصول على جميع اللوحات للسيرفر
    const panels = await panelService.getPanelsByGuild(interaction.guild.id);

    if (!selectedPanel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      if (!interaction.replied && !interaction.deferred) {
        return interaction.reply({
          content: errorMessage,
          flags: MessageFlags.Ephemeral
        });
      }
    }

    // طباعة معلومات التشخيص
    console.log(`تم اختيار الإجراء: ${state.selectedAction}, customId: ${interaction.customId}`);

    // التحقق من نوع القائمة المنسدلة
    if (interaction.customId === 'select_panel_action_extra') {
      console.log('تم اختيار إجراء من القائمة المنسدلة الإضافية');
    }

    // معالجة الإجراء المحدد
    switch (state.selectedAction) {
      case 'send_panel':
        await handleSendPanel(interaction, client, state, selectedPanel);
        break;
      case 'edit_panel_name':
        await handleEditPanelName(interaction, client, state, selectedPanel);
        break;
      case 'edit_panel_description':
        await handleEditPanelDescription(interaction, client, state, selectedPanel);
        break;
      case 'edit_welcome_message':
        await handleEditWelcomeMessage(interaction, client, state, selectedPanel);
        break;
      case 'edit_display_type':
        await handleEditDisplayType(interaction, client, state, selectedPanel);
        break;
      case 'add_category':
        await handleAddCategory(interaction, client, state, selectedPanel);
        break;
      case 'edit_support_roles':
        await handleEditSupportRoles(interaction, client, state, selectedPanel);
        break;
      case 'edit_transcript_channel':
        await handleEditTranscriptChannel(interaction, client, state, selectedPanel);
        break;
      case 'edit_ticket_category':
        await handleEditTicketCategory(interaction, client, state, selectedPanel);
        break;
      case 'edit_ticket_name':
        await handleEditTicketName(interaction, client, state, selectedPanel);
        break;
      case 'delete_panel':
        await handleDeletePanel(interaction, client, state, selectedPanel);
        break;
      default:
        // الحصول على الترجمات
        const title = await client.translate(interaction.guild.id, 'panel_editor.title');
        const description = await client.translate(interaction.guild.id, 'panel_editor.description');
        const unsavedChanges = await client.translate(interaction.guild.id, 'panel_editor.unsaved_changes');

        // إذا لم يتم التعرف على الإجراء، عرض قائمة الإجراءات مرة أخرى
        const embed = new EmbedBuilder()
          .setColor(client.config.embedColor)
          .setTitle(title)
          .setDescription(description)
          .addFields(
            { name: '\u200B', value: unsavedChanges }
          );

        const row1 = new ActionRowBuilder()
          .addComponents(
            new StringSelectMenuBuilder()
              .setCustomId('select_panel_to_edit')
              .setPlaceholder(`[${selectedPanel.panelId.substring(0, 8)}...] ${selectedPanel.name}`)
              .addOptions(panels.map(panel => ({
                label: panel.name,
                value: panel.panelId,
                description: `Panel ID: ${panel.panelId.substring(0, 16)}...`
              })))
          );

        // الحصول على ترجمات الإجراءات
        const actionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.action_placeholder');
        const sendPanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.label');
        const sendPanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.send_panel.description');
        const editNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.label');
        const editNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_name.description');
        const editDescriptionLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.label');
        const editDescriptionDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_description.description');
        const editWelcomeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.label');
        const editWelcomeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_welcome.description');
        const editDisplayTypeLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.label');
        const editDisplayTypeDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_display_type.description');
        const addCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.label');
        const addCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.add_category.description');
        const editRolesLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.label');
        const editRolesDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_roles.description');
        const editTranscriptLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.label');
        const editTranscriptDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_transcript.description');
        const editCategoryLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.label');
        const editCategoryDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_category.description');
        const editTicketNameLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_ticket_name.label');
        const editTicketNameDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.edit_ticket_name.description');
        const deletePanelLabel = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.label');
        const deletePanelDesc = await client.translate(interaction.guild.id, 'panel_editor.actions.delete_panel.description');

        // القائمة الأولى: الإجراءات الأساسية
        const row2 = new ActionRowBuilder()
          .addComponents(
            new StringSelectMenuBuilder()
              .setCustomId('select_panel_action')
              .setPlaceholder(actionPlaceholder)
              .addOptions([
                {
                  label: sendPanelLabel,
                  value: 'send_panel',
                  description: sendPanelDesc,
                  emoji: '0️⃣'
                },
                {
                  label: editNameLabel,
                  value: 'edit_panel_name',
                  description: editNameDesc,
                  emoji: '1️⃣'
                },
                {
                  label: editDescriptionLabel,
                  value: 'edit_panel_description',
                  description: editDescriptionDesc,
                  emoji: '2️⃣'
                },
                {
                  label: editWelcomeLabel,
                  value: 'edit_welcome_message',
                  description: editWelcomeDesc,
                  emoji: '3️⃣'
                },
                {
                  label: editDisplayTypeLabel,
                  value: 'edit_display_type',
                  description: editDisplayTypeDesc,
                  emoji: '4️⃣'
                },
                {
                  label: addCategoryLabel,
                  value: 'add_category',
                  description: addCategoryDesc,
                  emoji: '5️⃣'
                }
              ])
          );

        // القائمة الثانية: إجراءات إضافية
        const row2b = new ActionRowBuilder()
          .addComponents(
            new StringSelectMenuBuilder()
              .setCustomId('select_panel_action_extra')
              .setPlaceholder("إجراءات إضافية...")
              .addOptions([
                {
                  label: editRolesLabel,
                  value: 'edit_support_roles',
                  description: editRolesDesc,
                  emoji: '6️⃣'
                },
                {
                  label: editTranscriptLabel,
                  value: 'edit_transcript_channel',
                  description: editTranscriptDesc,
                  emoji: '7️⃣'
                },
                {
                  label: editTicketNameLabel,
                  value: 'edit_ticket_name',
                  description: editTicketNameDesc,
                  emoji: '8️⃣'
                },
                {
                  label: editCategoryLabel,
                  value: 'edit_ticket_category',
                  description: editCategoryDesc,
                  emoji: '9️⃣'
                },
                {
                  label: deletePanelLabel,
                  value: 'delete_panel',
                  description: deletePanelDesc,
                  emoji: '🔟'
                }
              ])
          );

        // الحصول على ترجمات الأزرار
        const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
        const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

        const row3 = new ActionRowBuilder()
          .addComponents(
            new ButtonBuilder()
              .setCustomId('setup_back')
              .setLabel(backButton)
              .setStyle(ButtonStyle.Secondary)
              .setEmoji('◀️'),
            new ButtonBuilder()
              .setCustomId('save_panel_changes')
              .setLabel(saveButton)
              .setStyle(ButtonStyle.Secondary)
          );

        await interaction.update({ embeds: [embed], components: [row1, row2, row2b, row3] });
        break;
    }
  }
};

// دالة لمعالجة إرسال اللوحة إلى قناة
async function handleSendPanel(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.send_panel.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.send_panel.description', { panel_name: selectedPanel.name });
  const selectPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.send_panel.select_placeholder');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
  const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

  // الحصول على جميع القنوات النصية في السيرفر
  const textChannels = interaction.guild.channels.cache
    .filter(channel => channel.type === 0) // TextChannel
    .map(channel => ({
      label: channel.name,
      value: channel.id,
      description: `Channel ID: ${channel.id}`
    }));

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_channel_for_panel')
        .setPlaceholder(selectPlaceholder)
        .addOptions(textChannels.slice(0, 25))
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('save_panel_changes')
        .setLabel(saveButton)
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لمعالجة تعديل اسم اللوحة
async function handleEditPanelName(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const modalTitle = await client.translate(interaction.guild.id, 'panel_editor.edit_name_modal.title');
  const inputLabel = await client.translate(interaction.guild.id, 'panel_editor.edit_name_modal.label');
  const inputPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.edit_name_modal.placeholder');

  const modal = new ModalBuilder()
    .setCustomId('edit_panel_name_modal')
    .setTitle(modalTitle);

  const panelNameInput = new TextInputBuilder()
    .setCustomId('panel_name_input')
    .setLabel(inputLabel)
    .setStyle(TextInputStyle.Short)
    .setPlaceholder(inputPlaceholder)
    .setValue(selectedPanel.name)
    .setRequired(true);

  const firstActionRow = new ActionRowBuilder().addComponents(panelNameInput);
  modal.addComponents(firstActionRow);

  await interaction.showModal(modal);
}

// دالة لمعالجة تعديل أدوار فريق الدعم
async function handleEditSupportRoles(interaction, client, state, selectedPanel) {
  // الحصول على جميع الأدوار في السيرفر
  const roles = interaction.guild.roles.cache
    .filter(role => !role.managed && role.id !== interaction.guild.id)
    .map(role => ({
      label: role.name,
      value: role.id,
      description: `Role ID: ${role.id}`
    }));

  // الحصول على أدوار الدعم الحالية
  const supportRoles = selectedPanel.supportRoles || [];

  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.description');
  const selectedRolesLabel = await client.translate(interaction.guild.id, 'setup.steps.step2.selected_roles');
  const noneSelected = await client.translate(interaction.guild.id, 'setup.steps.step2.none_selected');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  const selectedRolesText = supportRoles.length > 0
    ? supportRoles.map(roleId => `<@&${roleId}>`).join(', ')
    : noneSelected;

  embed.addFields({ name: selectedRolesLabel, value: selectedRolesText });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_support_roles_for_panel')
        .setPlaceholder(await client.translate(interaction.guild.id, 'setup.steps.step2.select_placeholder'))
        .setMinValues(0)
        .setMaxValues(roles.length > 25 ? 25 : roles.length)
        .addOptions(roles.slice(0, 25))
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(await client.translate(interaction.guild.id, 'setup.back_button'))
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('save_panel_changes')
        .setLabel(await client.translate(interaction.guild.id, 'setup.save_button'))
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لمعالجة تعديل قناة النسخ
async function handleEditTranscriptChannel(interaction, client, state, selectedPanel) {
  // الحصول على جميع القنوات النصية في السيرفر
  const textChannels = interaction.guild.channels.cache
    .filter(channel => channel.type === 0) // TextChannel
    .map(channel => ({
      label: channel.name,
      value: channel.id,
      description: `Channel ID: ${channel.id}`
    }));

  // الحصول على قناة النسخ الحالية
  const transcriptChannel = selectedPanel.transcriptChannel || '';

  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.description');
  const selectedChannelLabel = await client.translate(interaction.guild.id, 'setup.steps.step4.selected_channel');
  const notSelected = await client.translate(interaction.guild.id, 'setup.steps.step4.not_selected');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  const selectedChannelText = transcriptChannel
    ? `<#${transcriptChannel}>`
    : notSelected;

  embed.addFields({ name: selectedChannelLabel, value: selectedChannelText });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_transcript_channel_for_panel')
        .setPlaceholder(await client.translate(interaction.guild.id, 'setup.steps.step4.select_placeholder'))
        .addOptions(textChannels.slice(0, 25))
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(await client.translate(interaction.guild.id, 'setup.back_button'))
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('save_panel_changes')
        .setLabel(await client.translate(interaction.guild.id, 'setup.save_button'))
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لمعالجة تعديل فئة التذاكر
async function handleEditTicketCategory(interaction, client, state, selectedPanel) {
  // الحصول على جميع فئات القنوات في السيرفر
  const categories = interaction.guild.channels.cache
    .filter(channel => channel.type === 4) // CategoryChannel
    .map(category => ({
      label: category.name,
      value: category.id,
      description: `Category ID: ${category.id}`
    }));

  // الحصول على فئة التذاكر الحالية
  const ticketCategory = selectedPanel.ticketCategory || '';

  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.description');
  const selectedCategoriesLabel = await client.translate(interaction.guild.id, 'setup.steps.step3.selected_categories');
  const noneSelected = await client.translate(interaction.guild.id, 'setup.steps.step3.none_selected');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  const selectedCategoryText = ticketCategory
    ? `<#${ticketCategory}>`
    : noneSelected;

  embed.addFields({ name: selectedCategoriesLabel, value: selectedCategoryText });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_ticket_category_for_panel')
        .setPlaceholder(await client.translate(interaction.guild.id, 'setup.steps.step3.select_placeholder'))
        .addOptions(categories.slice(0, 25))
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(await client.translate(interaction.guild.id, 'setup.back_button'))
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('save_panel_changes')
        .setLabel(await client.translate(interaction.guild.id, 'setup.save_button'))
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لمعالجة تعديل وصف اللوحة
async function handleEditPanelDescription(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const modalTitle = await client.translate(interaction.guild.id, 'panel_editor.edit_description_modal.title');
  const inputLabel = await client.translate(interaction.guild.id, 'panel_editor.edit_description_modal.label');
  const inputPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.edit_description_modal.placeholder');

  const modal = new ModalBuilder()
    .setCustomId('edit_panel_description_modal')
    .setTitle(modalTitle);

  const descriptionInput = new TextInputBuilder()
    .setCustomId('panel_description_input')
    .setLabel(inputLabel)
    .setStyle(TextInputStyle.Paragraph)
    .setPlaceholder(inputPlaceholder)
    .setValue(selectedPanel.description || 'انقر على الزر أدناه لإنشاء تذكرة دعم.')
    .setRequired(true);

  const firstActionRow = new ActionRowBuilder().addComponents(descriptionInput);
  modal.addComponents(firstActionRow);

  await interaction.showModal(modal);
}

// دالة لمعالجة تعديل رسالة الترحيب
async function handleEditWelcomeMessage(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const modalTitle = await client.translate(interaction.guild.id, 'panel_editor.edit_welcome_modal.title');
  const inputLabel = await client.translate(interaction.guild.id, 'panel_editor.edit_welcome_modal.label');
  const inputPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.edit_welcome_modal.placeholder');

  const modal = new ModalBuilder()
    .setCustomId('edit_welcome_message_modal')
    .setTitle(modalTitle);

  const welcomeInput = new TextInputBuilder()
    .setCustomId('welcome_message_input')
    .setLabel(inputLabel)
    .setStyle(TextInputStyle.Paragraph)
    .setPlaceholder(inputPlaceholder)
    .setValue(selectedPanel.welcomeMessage || 'مرحبًا {user}،\n\nشكرًا لإنشاء تذكرة دعم. سيقوم فريق الدعم لدينا بالرد عليك في أقرب وقت ممكن.\n\nيرجى وصف مشكلتك أو استفسارك بالتفصيل.')
    .setRequired(true);

  const firstActionRow = new ActionRowBuilder().addComponents(welcomeInput);
  modal.addComponents(firstActionRow);

  await interaction.showModal(modal);
}

// دالة لمعالجة تعديل نوع عرض اللوحة
async function handleEditDisplayType(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.display_type.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.display_type.description', { panel_name: selectedPanel.name });
  const dropdownImageLabel = await client.translate(interaction.guild.id, 'panel_editor.display_type.dropdown_image');
  const dropdownMessageLabel = await client.translate(interaction.guild.id, 'panel_editor.display_type.dropdown_message');
  const buttonsImageLabel = await client.translate(interaction.guild.id, 'panel_editor.display_type.buttons_image');
  const buttonsMessageLabel = await client.translate(interaction.guild.id, 'panel_editor.display_type.buttons_message');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
  const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

  // الحصول على نوع العرض الحالي بصيغة مقروءة
  let currentDisplayType = '';
  switch (selectedPanel.displayType) {
    case 'dropdown_image':
      currentDisplayType = dropdownImageLabel;
      break;
    case 'dropdown_message':
      currentDisplayType = dropdownMessageLabel;
      break;
    case 'buttons_image':
      currentDisplayType = buttonsImageLabel;
      break;
    case 'buttons_message':
      currentDisplayType = buttonsMessageLabel;
      break;
    default:
      currentDisplayType = dropdownMessageLabel;
  }

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description)
    .addFields({ name: 'نوع العرض الحالي', value: currentDisplayType });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_display_type')
        .setPlaceholder(currentDisplayType)
        .addOptions([
          {
            label: dropdownImageLabel,
            value: 'dropdown_image',
            description: 'Dropdown menu with image',
            emoji: '1️⃣'
          },
          {
            label: dropdownMessageLabel,
            value: 'dropdown_message',
            description: 'Dropdown menu with message',
            emoji: '2️⃣'
          },
          {
            label: buttonsImageLabel,
            value: 'buttons_image',
            description: 'Buttons with image',
            emoji: '3️⃣'
          },
          {
            label: buttonsMessageLabel,
            value: 'buttons_message',
            description: 'Buttons with message',
            emoji: '4️⃣'
          }
        ])
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('save_panel_changes')
        .setLabel(saveButton)
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لمعالجة إضافة فئة/زر للوحة
async function handleAddCategory(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.category_actions.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.category_actions.description');
  const actionPlaceholder = await client.translate(interaction.guild.id, 'panel_editor.category_actions.placeholder');
  const addNewLabel = await client.translate(interaction.guild.id, 'panel_editor.category_actions.add_new');
  const editLabel = await client.translate(interaction.guild.id, 'panel_editor.category_actions.edit');
  const deleteLabel = await client.translate(interaction.guild.id, 'panel_editor.category_actions.delete');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description);

  // إضافة معلومات عن الفئات الحالية
  if (selectedPanel.categories && selectedPanel.categories.length > 0) {
    const categoriesInfo = selectedPanel.categories.map((cat, index) =>
      `${index + 1}. ${cat.name}`
    ).join('\n');

    embed.addFields({ name: 'الفئات الحالية', value: categoriesInfo });
  } else {
    embed.addFields({ name: 'الفئات الحالية', value: 'لا توجد فئات حالية' });
  }

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_category_action')
        .setPlaceholder(actionPlaceholder)
        .addOptions([
          {
            label: addNewLabel,
            value: 'add_new_category',
            description: 'إضافة فئة/زر جديد',
            emoji: '1️⃣'
          },
          {
            label: editLabel,
            value: 'edit_category',
            description: 'تعديل فئة/زر حالي',
            emoji: '2️⃣'
          },
          {
            label: deleteLabel,
            value: 'delete_category',
            description: 'حذف فئة/زر حالي',
            emoji: '3️⃣'
          }
        ])
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️')
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لمعالجة تعديل اسم التذكرة
async function handleEditTicketName(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.description', { panel_name: selectedPanel.name });
  const usernameFormat = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.username_format');
  const usernameDescription = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.username_description');
  const numberedFormat = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.numbered_format');
  const numberedDescription = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.numbered_description');
  const currentFormatLabel = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.current_format');
  const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
  const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

  // الحصول على تنسيق اسم التذكرة الحالي
  const currentFormat = selectedPanel.ticketNameFormat || 'username';
  let currentFormatText = '';

  if (currentFormat === 'username') {
    currentFormatText = usernameFormat;
  } else if (currentFormat === 'numbered') {
    currentFormatText = numberedFormat;
  }

  const embed = new EmbedBuilder()
    .setColor(client.config.embedColor)
    .setTitle(title)
    .setDescription(description)
    .addFields({ name: currentFormatLabel, value: currentFormatText });

  const row1 = new ActionRowBuilder()
    .addComponents(
      new StringSelectMenuBuilder()
        .setCustomId('select_ticket_name_format')
        .setPlaceholder(currentFormatText)
        .addOptions([
          {
            label: usernameFormat,
            value: 'username',
            description: usernameDescription,
            emoji: '👤'
          },
          {
            label: numberedFormat,
            value: 'numbered',
            description: numberedDescription,
            emoji: '🔢'
          }
        ])
    );

  const row2 = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(backButton)
        .setStyle(ButtonStyle.Secondary)
        .setEmoji('◀️'),
      new ButtonBuilder()
        .setCustomId('save_panel_changes')
        .setLabel(saveButton)
        .setStyle(ButtonStyle.Secondary)
    );

  await interaction.update({ embeds: [embed], components: [row1, row2] });
}

// دالة لمعالجة حذف اللوحة
async function handleDeletePanel(interaction, client, state, selectedPanel) {
  // الحصول على الترجمات
  const title = await client.translate(interaction.guild.id, 'panel_editor.delete_confirmation.title');
  const description = await client.translate(interaction.guild.id, 'panel_editor.delete_confirmation.description', { panel_name: selectedPanel.name });
  const cancelButton = await client.translate(interaction.guild.id, 'panel_editor.delete_confirmation.cancel_button');
  const deleteButton = await client.translate(interaction.guild.id, 'panel_editor.delete_confirmation.delete_button');

  const embed = new EmbedBuilder()
    .setColor('#ff0000')
    .setTitle(title)
    .setDescription(description);

  const row = new ActionRowBuilder()
    .addComponents(
      new ButtonBuilder()
        .setCustomId('setup_back')
        .setLabel(cancelButton)
        .setStyle(ButtonStyle.Secondary),
      new ButtonBuilder()
        .setCustomId('confirm_delete_panel')
        .setLabel(deleteButton)
        .setStyle(ButtonStyle.Danger)
    );

  await interaction.update({ embeds: [embed], components: [row] });
}
