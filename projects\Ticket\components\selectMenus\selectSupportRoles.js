const { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Row<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ton<PERSON><PERSON><PERSON>, StringSelectMenuBuilder, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');

module.exports = {
  id: 'select_support_roles_for_panel',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // تحديث أدوار الدعم المحددة
    state.supportRoles = interaction.values;
    setupStates.set(interaction.user.id, state);

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'panel_editor.title');
    const description = await client.translate(interaction.guild.id, 'panel_editor.description');
    const selectedRolesLabel = await client.translate(interaction.guild.id, 'setup.steps.step2.selected_roles');
    const noneSelected = await client.translate(interaction.guild.id, 'setup.steps.step2.none_selected');

    // إعادة عرض الخطوة الحالية مع القيم المحدثة
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description);

    // الحصول على جميع الأدوار في السيرفر
    const roles = interaction.guild.roles.cache
      .filter(role => !role.managed && role.id !== interaction.guild.id)
      .map(role => ({
        label: role.name,
        value: role.id,
        description: `Role ID: ${role.id}`
      }));

    const selectedRolesText = state.supportRoles.length > 0
      ? state.supportRoles.map(roleId => `<@&${roleId}>`).join(', ')
      : noneSelected;

    embed.addFields({ name: selectedRolesLabel, value: selectedRolesText });

    // الحصول على ترجمات إضافية
    const selectPlaceholder = await client.translate(interaction.guild.id, 'setup.steps.step2.select_placeholder');
    const backButton = await client.translate(interaction.guild.id, 'setup.back_button');
    const saveButton = await client.translate(interaction.guild.id, 'setup.save_button');

    const row1 = new ActionRowBuilder()
      .addComponents(
        new StringSelectMenuBuilder()
          .setCustomId('select_support_roles_for_panel')
          .setPlaceholder(selectPlaceholder)
          .setMinValues(0)
          .setMaxValues(roles.length > 25 ? 25 : roles.length)
          .addOptions(roles.slice(0, 25))
      );

    const row2 = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('setup_back')
          .setLabel(backButton)
          .setStyle(ButtonStyle.Secondary)
          .setEmoji('◀️'),
        new ButtonBuilder()
          .setCustomId('save_panel_changes')
          .setLabel(saveButton)
          .setStyle(ButtonStyle.Secondary)
      );

    try {
      // تحقق مما إذا كان التفاعل قد تم الرد عليه بالفعل
      if (interaction.replied || interaction.deferred) {
        await interaction.editReply({
          embeds: [embed],
          components: [row1, row2]
        });
      } else {
        await interaction.update({
          embeds: [embed],
          components: [row1, row2]
        });
      }
    } catch (error) {
      console.error('Error updating support roles interaction:', error);
    }
  }
};
