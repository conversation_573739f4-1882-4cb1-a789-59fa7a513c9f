const { Modal<PERSON>uilder, TextInputBuilder, TextInputStyle, ActionRowBuilder, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');

module.exports = {
  id: 'set_panel_name',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // الحصول على الترجمات
    const modalTitle = await client.translate(interaction.guild.id, 'setup.panel_name_modal.title');
    const inputLabel = await client.translate(interaction.guild.id, 'setup.panel_name_modal.label');
    const inputPlaceholder = await client.translate(interaction.guild.id, 'setup.panel_name_modal.placeholder');

    // إنشاء نموذج لإدخال اسم اللوحة
    const modal = new ModalBuilder()
      .setCustomId('panel_name_modal')
      .setTitle(modalTitle);

    const panelNameInput = new TextInputBuilder()
      .setCustomId('panel_name_input')
      .setLabel(inputLabel)
      .setStyle(TextInputStyle.Short)
      .setPlaceholder(inputPlaceholder)
      .setValue(state.panelName || 'New Panel')
      .setRequired(true);

    const firstActionRow = new ActionRowBuilder().addComponents(panelNameInput);
    modal.addComponents(firstActionRow);

    await interaction.showModal(modal);
  }
};
