const { SlashCommandBuilder, PermissionFlagsBits, MessageFlags } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('setname')
    .setDescription('Change the bot username')
    .addStringOption(option =>
      option.setName('name')
        .setDescription('New username for the bot')
        .setRequired(true)
        .setMaxLength(32)
    )
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction, client) {
    try {
      // التحقق من الصلاحيات
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return await interaction.reply({
          content: '❌ تحتاج إلى صلاحية المدير لاستخدام هذا الأمر.',
          flags: MessageFlags.Ephemeral
        });
      }

      const newName = interaction.options.getString('name');
      const oldName = client.user.username;

      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      // تغيير اسم البوت
      await client.user.setUsername(newName);

      await interaction.editReply({
        content: `✅ تم تغيير اسم البوت بنجاح!\n\n📝 **الاسم القديم:** ${oldName}\n🆕 **الاسم الجديد:** ${newName}\n\n⚠️ **ملاحظة:** قد يستغرق التغيير بضع دقائق ليظهر في جميع السيرفرات.`
      });

      console.log(`Bot username changed from "${oldName}" to "${newName}" by ${interaction.user.tag}`);

    } catch (error) {
      console.error('Error in setname command:', error);
      
      let errorMessage = '❌ حدث خطأ أثناء تغيير اسم البوت.';
      
      if (error.code === 50035) {
        errorMessage = '❌ الاسم المدخل غير صالح. يجب أن يكون بين 2-32 حرف ولا يحتوي على رموز غير مسموحة.';
      } else if (error.code === 50013) {
        errorMessage = '❌ البوت لا يملك الصلاحيات اللازمة لتغيير اسمه.';
      } else if (error.message.includes('rate limit')) {
        errorMessage = '❌ تم تغيير الاسم مؤخراً. يرجى المحاولة مرة أخرى بعد ساعة.';
      }

      if (interaction.deferred) {
        await interaction.editReply({ content: errorMessage });
      } else {
        await interaction.reply({ content: errorMessage, flags: MessageFlags.Ephemeral });
      }
    }
  },
};
