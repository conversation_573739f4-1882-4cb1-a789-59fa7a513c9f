const { Embed<PERSON><PERSON>er, PermissionFlagsBits, MessageFlags } = require('discord.js');
const { ActionRowBuilder, ButtonStyle , ButtonBuilder } = require('discord.js');
const commandPowers = require('../../Data/command-powers');
const log = require('../../Data/log');
const moment = require('moment');
const channel_data = require('../../Data/Auto_delete');
const commandName = 'setclear';
const owners = require('../../Data/owner');

module.exports = {
  name: 'setclear',
  async run(client, message, args) {
    const doc = await commandPowers
    .findOne({ guildId: message.guild.id, name: commandName })
    .exec();
if (!doc) {
    if (!message.member.permissions.has(PermissionFlagsBits.Administrator))
        return;
    canHe = true;
} else {
    const HasEnRole = message.member.roles.cache.some((role) =>
        doc.EnRoleIds.includes(role.id)
    );
    const HasntEnRole = message.member.roles.cache.some((role) =>
        doc.DisRoleIds.includes(role.id)
    );
    if (HasntEnRole) {
        canHe = false;
    } else if (HasEnRole) {
        canHe = true;
    } else {
        if (!message.member.permissions.has(PermissionFlagsBits.Administrator))
            return;
        canHe = true;
    }
}
const doc8 = await owners
    .findOne({ guildId: message.guild.id, userId: message.author.id })
    .exec();
if (doc8) {
    canHe = true;
}
if (canHe === false)
    return message.reply("> ⚠️ - لا يمكنك استخدام هذا الامر");

    const target = message.content.split(' ').slice('1').join(' ');
    const channel = message.mentions.channels.first() || message.guild.channels.cache.get(target);
    const bot = message.guild.members.cache.get(client.user.id);
    const args1 = message.content.split(' ').slice(2).join(' ');

    if (!channel) return message.reply('> منشن الشات');

    if (channel) {
        const a = await channel_data.findOne({ guildid: message.guild.id, ChannelId: channel.id }) || await channel_data.create({ guildid: message.guild.id, ChannelId: channel.id })

      const embed = new EmbedBuilder()
        .setThumbnail(message.author.avatarURL())
        .setAuthor({
          name: message.author.tag,
          iconURL: message.author.avatarURL(),
          url: message.author.avatarURL(),
        })
        .setTimestamp()
        .setFooter({ text: message.guild.name })
        .setTitle(" قائمة اعداد المسح التلقائي")
        .setDescription(`📍 **القناة:** ${channel}\n⏰ **مدة المسح الحالية:** ${a.Duration || 10} ثانية`)
        .addFields([
          { name: '📝 النص الحالي', value: a.Text || 'لا يوجد نص', inline: true },
          { name: '🖼️ الصورة الحالية', value: a.Image ? 'تم تعيين صورة' : 'لا توجد صورة', inline: true }
        ]);

      const confirm = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId('text')
          .setLabel(" ارسال نص ")
          .setStyle(ButtonStyle.Secondary)
      )
.addComponents(
        new ButtonBuilder()
          .setCustomId('img')
          .setLabel(" ارسال صورة ")
          .setStyle(ButtonStyle.Secondary)

      )
      .addComponents(
        new ButtonBuilder()
          .setCustomId('img-txt')
          .setLabel(" ارسال صورة مع نص " )
          .setStyle(ButtonStyle.Secondary)

      )
      .addComponents(
        new ButtonBuilder()
          .setCustomId('without')
          .setLabel('بدون')
          .setStyle(ButtonStyle.Primary)
      )

      const confirm2 = new ActionRowBuilder().addComponents(
        new ButtonBuilder()
          .setCustomId('set_duration')
          .setLabel("⏰ تعيين مدة المسح")
          .setStyle(ButtonStyle.Success)
      )
      .addComponents(
        new ButtonBuilder()
          .setCustomId('delete')
          .setLabel("🗑️ إعادة ضبط المحتوى")
          .setStyle(ButtonStyle.Secondary)
      )
      .addComponents(
        new ButtonBuilder()
          .setCustomId('delete_all')
          .setLabel("❌ حذف النظام بالكامل")
          .setStyle(ButtonStyle.Danger)
      )

      message.channel
        .send({ embeds: [embed], components: [confirm, confirm2] })
        .then(async (sent) => {
          const collectorFilter = (i) => i.user.id === message.author.id;

          try {
            const confirmation = await sent.awaitMessageComponent({
              filter: collectorFilter,
              time: 60000,
            });

          const collectorText = message.channel.createMessageCollector({
            filter: (m) => m.author.id === message.author.id && m.content.length > 0,
            time: 180000,
            max: 1,
          });

          const collectorImg = message.channel.createMessageCollector({
            filter: (m) => m.author.id === message.author.id && m.attachments.size > 0,
            time: 180000,
            max: 1,
          });

          const collectorImg_txt = message.channel.createMessageCollector({
            filter: (m) => m.author.id === message.author.id && m.attachments.size > 0 && m.content.length > 0,
            time: 180000,
            max: 1,
          });

          if (confirmation.customId == 'img-txt') {
            // رسالة تأكيد فورية
            await confirmation.reply({ content: '✅ تم اختيار "صورة مع نص"', flags: MessageFlags.Ephemeral });

            const exampleEmbed = new EmbedBuilder()
              .setTitle(' لديك 3 دقائق لارسال النص والصوره معا في رسالة واحده')
              .setColor('Red')
              .setDescription('ارسل الرساله في خلال 3 دقائق');

            sent.edit({ embeds: [exampleEmbed], components: [] });

            collectorImg_txt.on('collect', async (m) => {



            const img = m.attachments.first()
            if(!img) return  sent.edit({ embeds: [], components: [], content : `ارسل صوره صالحه` });
           const txt = m.content;
               a.Text = txt
               a.Image = img.url
            await  a.save()
              const exampleEmbed = new EmbedBuilder()
                .setTitle("تم الاستلام")
                .setColor('Green')
                .setDescription(`النص : ${m.content}`)
                .setImage(img.url);

              sent.edit({ embeds: [exampleEmbed], components: [] });
              m.delete()
            });

            collectorImg_txt.on('end', (collected) => {
              if (collected.size === 0) {
                sent.delete();
                message.channel.send('**انتهى الوقت. يرجى إعادة المحاولة.**');
              }
            });


          }


          if (confirmation.customId == 'text') {
            // رسالة تأكيد فورية
            await confirmation.reply({ content: '✅ تم اختيار "ارسال نص"', flags: MessageFlags.Ephemeral });

            const exampleEmbed = new EmbedBuilder()
              .setTitle('لديك 3 دقائق لارسال النص')
              .setColor('Red')
              .setDescription('ارسل النص في خلال 3 دقائق');

            sent.edit({ embeds: [exampleEmbed], components: [] });

            collectorText.on('collect', async (m) => {
              const txt = m.content;
                a.Text = txt
            await  a.save()

              const exampleEmbed = new EmbedBuilder()
                .setTitle('تم استلام النص')
                .setColor('Green')
                .setDescription(`النص : ${txt}`);

              sent.edit({ embeds: [exampleEmbed], components: [] });
              m.delete()

            });

            collectorText.on('end', (collected) => {
              if (collected.size === 0) {
                sent.delete();
                message.channel.send('**انتهى الوقت. يرجى إعادة المحاولة.**');
              }
            });
          }


          if (confirmation.customId == 'delete') {
            // رسالة تأكيد فورية
            await confirmation.reply({ content: '✅ تم اختيار "إعادة ضبط الاعدادات"', flags: MessageFlags.Ephemeral });

            await a.deleteOne();

            const exampleEmbed = new EmbedBuilder()
            .setTitle('✅ تم إعادة ضبط نظام المسح التلقائي')
            .setColor('Green')
            .setDescription('تم حذف جميع الإعدادات بما في ذلك المدة والنص والصورة')
            .addFields([
              { name: '🗑️ ما تم حذفه', value: '• النص المحفوظ\n• الصورة المحفوظة\n• مدة المسح المخصصة\n• جميع إعدادات القناة', inline: false },
              { name: '📋 ملاحظة', value: 'لإعادة تفعيل المسح التلقائي، استخدم الأمر مرة أخرى', inline: false }
            ]);

          sent.edit({ embeds: [exampleEmbed], components: [] });
          }

          if (confirmation.customId == 'without') {
            // رسالة تأكيد فورية
            await confirmation.reply({ content: '✅ تم اختيار "بدون محتوى"', flags: MessageFlags.Ephemeral });

            const exampleEmbed = new EmbedBuilder()
            .setTitle('✅ تم إعداد المسح التلقائي بدون محتوى')
            .setColor('Green')
            .setDescription('سيتم مسح الرسائل بدون إرسال أي محتوى بديل')
            .addFields([
              { name: '⚙️ الإعداد', value: 'مسح تلقائي بدون محتوى', inline: true },
              { name: '⏰ المدة', value: `${a.Duration || 10} ثانية`, inline: true }
            ]);

          sent.edit({ embeds: [exampleEmbed], components: [] });
          }

          if (confirmation.customId == 'set_duration') {
            // رسالة تأكيد فورية
            await confirmation.reply({ content: '✅ تم اختيار "تعيين مدة المسح"', flags: MessageFlags.Ephemeral });

            const exampleEmbed = new EmbedBuilder()
              .setTitle('⏰ تعيين مدة المسح')
              .setColor('Blue')
              .setDescription('أرسل مدة المسح بالثواني (مثال: 10 للمسح بعد 10 ثواني)\nالمدة الحالية: ' + (a.Duration || 10) + ' ثانية')
              .addFields([
                { name: '📝 أمثلة', value: '`5` - مسح بعد 5 ثواني\n`30` - مسح بعد 30 ثانية\n`60` - مسح بعد دقيقة واحدة', inline: false }
              ]);

            sent.edit({ embeds: [exampleEmbed], components: [] });

            const collectorDuration = message.channel.createMessageCollector({
              filter: (m) => m.author.id === message.author.id && m.content.length > 0,
              time: 60000,
              max: 1,
            });

            collectorDuration.on('collect', async (m) => {
              const duration = parseInt(m.content);

              if (isNaN(duration) || duration < 1 || duration > 3600) {
                const errorEmbed = new EmbedBuilder()
                  .setTitle('❌ خطأ في المدة')
                  .setColor('Red')
                  .setDescription('يجب أن تكون المدة رقم بين 1 و 3600 ثانية (ساعة واحدة)');

                sent.edit({ embeds: [errorEmbed], components: [] });
                m.delete();
                return;
              }

              a.Duration = duration;
              await a.save();

              // إعادة تحميل مؤقت القناة بالمدة الجديدة
              if (client.reloadChannelTimer) {
                client.reloadChannelTimer(channel.id);
              }

              const successEmbed = new EmbedBuilder()
                .setTitle('✅ تم تعيين مدة المسح')
                .setColor('Green')
                .setDescription(`تم تعيين مدة المسح إلى: **${duration} ثانية**`)
                .addFields([
                  { name: '⏰ المدة الجديدة', value: `${duration} ثانية`, inline: true },
                  { name: '📍 القناة', value: `<#${channel.id}>`, inline: true },
                  { name: '🔄 الحالة', value: 'تم تطبيق المدة الجديدة فوراً', inline: false }
                ]);

              sent.edit({ embeds: [successEmbed], components: [] });
              m.delete();
            });

            collectorDuration.on('end', (collected) => {
              if (collected.size === 0) {
                const timeoutEmbed = new EmbedBuilder()
                  .setTitle('⏰ انتهى الوقت')
                  .setColor('Red')
                  .setDescription('انتهى الوقت المحدد. يرجى إعادة المحاولة.');

                sent.edit({ embeds: [timeoutEmbed], components: [] });
              }
            });
          }

          if (confirmation.customId == 'img') {
            // رسالة تأكيد فورية
            await confirmation.reply({ content: '✅ تم اختيار "ارسال صورة"', flags: MessageFlags.Ephemeral });

            const exampleEmbed = new EmbedBuilder()
              .setTitle('لديك 3 دقائق لارسل الصوره')
              .setColor('Red')
              .setDescription('ارسل الصوره في خلال 3 دقائق');

            sent.edit({ embeds: [exampleEmbed], components: [] });

            collectorImg.on('collect', async (m) => {
              const atth = m.attachments.first();
              const atthUrl = atth.url;
              a.Image = atthUrl
              await  a.save()

              const exampleEmbed = new EmbedBuilder()
                .setTitle('تم استلام الصوره')
                .setColor('Green')
                .setImage(atthUrl);

              sent.edit({ embeds: [exampleEmbed], components: [] });
              m.delete()

            });

            collectorImg.on('end', (collected) => {
              if (collected.size === 0) {
                sent.delete();
                message.channel.send('**انتهى الوقت. يرجى إعادة المحاولة.**');
              }
            });
          }
          } catch (error) {
            console.error('Error in setclear interaction:', error);
            const errorEmbed = new EmbedBuilder()
              .setTitle('❌ انتهى الوقت')
              .setColor('Red')
              .setDescription('انتهى الوقت المحدد للتفاعل. يرجى إعادة استخدام الأمر.');

            sent.edit({ embeds: [errorEmbed], components: [] }).catch(() => {});
          }
        });


    }
  },
};
