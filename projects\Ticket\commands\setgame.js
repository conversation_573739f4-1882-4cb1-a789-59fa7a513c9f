const { SlashCommandBuilder, PermissionFlagsBits, ActivityType, MessageFlags } = require('discord.js');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('setgame')
    .setDescription('Change the bot activity status')
    .addStringOption(option =>
      option.setName('activity')
        .setDescription('The activity text to display')
        .setRequired(true)
        .setMaxLength(128)
    )
    .addStringOption(option =>
      option.setName('type')
        .setDescription('Type of activity')
        .setRequired(false)
        .addChoices(
          { name: '🎮 Playing', value: 'playing' },
          { name: '🎵 Listening', value: 'listening' },
          { name: '📺 Watching', value: 'watching' },
          { name: '🔴 Streaming', value: 'streaming' },
          { name: '🏆 Competing', value: 'competing' }
        )
    )
    .addStringOption(option =>
      option.setName('url')
        .setDescription('Stream URL (only for streaming type)')
        .setRequired(false)
    )
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction, client) {
    try {
      // التحقق من الصلاحيات
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return await interaction.reply({
          content: '❌ تحتاج إلى صلاحية المدير لاستخدام هذا الأمر.',
          flags: MessageFlags.Ephemeral
        });
      }

      const activity = interaction.options.getString('activity');
      const type = interaction.options.getString('type') || 'playing';
      const url = interaction.options.getString('url');

      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      // تحديد نوع النشاط
      let activityType;
      let activityName;

      switch (type) {
        case 'playing':
          activityType = ActivityType.Playing;
          activityName = '🎮 Playing';
          break;
        case 'listening':
          activityType = ActivityType.Listening;
          activityName = '🎵 Listening to';
          break;
        case 'watching':
          activityType = ActivityType.Watching;
          activityName = '📺 Watching';
          break;
        case 'streaming':
          activityType = ActivityType.Streaming;
          activityName = '🔴 Streaming';
          break;
        case 'competing':
          activityType = ActivityType.Competing;
          activityName = '🏆 Competing in';
          break;
        default:
          activityType = ActivityType.Playing;
          activityName = '🎮 Playing';
      }

      // إعداد النشاط
      const activityOptions = {
        name: activity,
        type: activityType
      };

      // إضافة URL للبث المباشر
      if (type === 'streaming' && url) {
        // التحقق من صحة رابط البث
        if (!url.includes('twitch.tv') && !url.includes('youtube.com')) {
          return await interaction.editReply({
            content: '❌ رابط البث يجب أن يكون من Twitch أو YouTube.'
          });
        }
        activityOptions.url = url;
      }

      // تطبيق النشاط الجديد
      await client.user.setActivity(activityOptions);

      let responseMessage = `✅ تم تغيير حالة البوت بنجاح!\n\n📊 **النوع:** ${activityName}\n📝 **النص:** ${activity}`;
      
      if (type === 'streaming' && url) {
        responseMessage += `\n🔗 **رابط البث:** ${url}`;
      }

      responseMessage += '\n\n⚠️ **ملاحظة:** التغيير سيظهر فوراً في جميع السيرفرات.';

      await interaction.editReply({
        content: responseMessage
      });

      console.log(`Bot activity changed by ${interaction.user.tag}: ${activityName} ${activity}${url ? ` (${url})` : ''}`);

    } catch (error) {
      console.error('Error in setgame command:', error);
      
      let errorMessage = '❌ حدث خطأ أثناء تغيير حالة البوت.';
      
      if (error.code === 50035) {
        errorMessage = '❌ النص المدخل غير صالح. يجب أن يكون أقل من 128 حرف.';
      } else if (error.code === 50013) {
        errorMessage = '❌ البوت لا يملك الصلاحيات اللازمة لتغيير حالته.';
      }

      if (interaction.deferred) {
        await interaction.editReply({ content: errorMessage });
      } else {
        await interaction.reply({ content: errorMessage, flags: MessageFlags.Ephemeral });
      }
    }
  },
};
