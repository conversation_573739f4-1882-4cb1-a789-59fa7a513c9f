{"setup": {"title": "Ticket AR basic config editor", "description": "This is the basic config editor for setup and small changes,\nfor a complete editor visit the dashboard", "help_text": "If you have any questions, please review the /help command.", "edit_panels_button": "Edit & Manage Panels", "create_panel_button": "Create A Panel", "steps": {"step1": {"title": "Step 1/5 Set the panel name", "description": "Use the button to set the panel name and continue (This can be changed later)", "current_name": "Current Name", "set_name_button": "Set name"}, "step2": {"title": "Step 2/5 Select the support team role(s)", "description": "The support roles will be automatically added to this panels tickets so they can assist people as needed.\n\nUse the dropdown to select roles.\n\nNot seeing your role? try searching for it inside the dropdown", "selected_roles": "Selected Role(s)", "none_selected": "None Selected..", "select_placeholder": "Select all the roles for your support team"}, "step3": {"title": "Step 3/5 Select the ticket category", "description": "The selected category(s) is where tickets will be created into.\n\nUse the dropdown to select the categories.\n\nNot seeing your channel? try searching for it inside the dropdown", "selected_categories": "Selected Category(s)", "none_selected": "None Selected..", "select_placeholder": "Select a category"}, "step4": {"title": "Step 4/5 Select the transcript channel", "description": "The selected channel is where transcripts will be saved into.\n\nUse the dropdown to select the channel.\n\nNot seeing your channel? try searching for it inside the dropdown", "selected_channel": "Selected Channel", "not_selected": "Not Selected..", "select_placeholder": "Select a channel"}, "step5": {"title": "Step 5/5 Send the panel into a channel", "description": "The ticket creation panel is what the community will use to create tickets.\n\nUse the dropdown to select the channel to send the panel into.\n\nNot seeing your channel? try searching for it inside the dropdown\n\nSending not working? run /debug in the channel it's being sent into.", "select_placeholder": "Select a channel"}}, "back_button": "Back", "continue_button": "Save & Continue", "finish_button": "Finish", "save_button": "Save", "panel_created": "Successfully created the ticket panel in channel {channel}.", "panel_name_modal": {"title": "Set Panel Name", "label": "Panel Name", "placeholder": "Enter the panel name"}}, "panel_editor": {"title": "Panel Editor & Manager", "description": "Select a panel to get started making changes.", "unsaved_changes": "Any unsaved changes will be discarded when switching panels", "select_placeholder": "Select a panel", "back_button": "Back", "save_button": "Save", "default_description": "Click the button below to create a support ticket.", "default_welcome_message": "Hello {user},\n\nThank you for creating a support ticket. Our support team will respond to you as soon as possible.\n\nPlease describe your issue or inquiry in detail.", "no_panels": {"label": "No panels available", "description": "Create a new panel using the Create A Panel button"}, "action_placeholder": "Select an action", "actions": {"send_panel": {"label": "Send panel to a channel", "description": "Send this panel to a channel"}, "edit_name": {"label": "Panel name", "description": "Edit the panel name"}, "edit_description": {"label": "Panel description", "description": "Edit the panel description"}, "edit_welcome": {"label": "Welcome message", "description": "Edit the welcome message after opening a ticket"}, "edit_display_type": {"label": "Panel display type", "description": "Change the panel display type"}, "add_category": {"label": "Add category/button", "description": "Add a new category or button to the panel"}, "edit_roles": {"label": "Support team roles", "description": "Edit support team roles"}, "edit_transcript": {"label": "Transcript Channel", "description": "Edit transcript channel"}, "edit_category": {"label": "Ticket Category", "description": "Edit ticket category"}, "edit_ticket_name": {"label": "Ticket Name", "description": "Edit ticket name format"}, "delete_panel": {"label": "Delete this panel", "description": "Delete this panel"}}, "delete_confirmation": {"title": "Delete Panel", "description": "Are you sure you want to delete the panel \"{panel_name}\"?\n\nThis action cannot be undone.", "cancel_button": "Cancel", "delete_button": "Delete"}, "panel_deleted": "Successfully deleted the panel \"{panel_name}\".", "changes_saved": "Changes saved successfully.", "panel_sent": "Panel has been sent to channel {channel} successfully.", "description_updated": "Panel description updated successfully.", "welcome_message_updated": "Welcome message updated successfully.", "display_type_updated": "Panel display type updated successfully.", "category_added": "Category/button added successfully.", "category_updated": "Category/button updated successfully.", "category_deleted": "Category/button deleted successfully.", "category_actions": {"title": "Manage Categories/Buttons", "description": "Choose the action you want to perform on categories/buttons.", "placeholder": "Select an action", "add_new": "Add new category/button", "edit": "Edit existing category/button", "delete": "Delete existing category/button"}, "edit_category": {"title": "Edit Category/Button", "description": "Choose the category/button you want to edit.", "select_placeholder": "Select a category/button"}, "delete_category": {"title": "Delete Category/Button", "description": "Choose the category/button you want to delete.", "select_placeholder": "Select a category/button"}, "delete_category_confirmation": {"title": "Confirm Category/Button Deletion", "description": "Are you sure you want to delete the category/button \"{category_name}\"?\n\nThis action cannot be undone.", "cancel_button": "Cancel", "delete_button": "Delete"}, "edit_category_modal": {"title": "Edit Category/Button", "name_label": "Category/Button Name", "name_placeholder": "Enter the category or button name", "description_label": "Category/Button Description", "description_placeholder": "Enter the category or button description", "emoji_label": "Emoji (Optional)", "emoji_placeholder": "Enter an emoji (e.g., 🎫) or leave empty"}, "edit_description_modal": {"title": "Edit Panel Description", "label": "Panel Description", "placeholder": "Enter the panel description"}, "edit_welcome_modal": {"title": "Edit Welcome Message", "label": "Welcome Message", "placeholder": "Enter the welcome message (use {user} to reference the user)"}, "display_type": {"title": "Edit Panel Display Type", "description": "Choose a display type for the panel \"{panel_name}\"", "dropdown_image": "Dropdown with image", "dropdown_message": "Dropdown with message", "buttons_image": "Buttons with image", "buttons_message": "Buttons with message"}, "image_url_modal": {"title": "Add Image URL", "label": "Image URL", "placeholder": "Enter the image URL (must be a direct link to an image)"}, "add_category_modal": {"title": "Add Category/Button", "name_label": "Category/Button Name", "name_placeholder": "Enter the category or button name", "description_label": "Category/Button Description", "description_placeholder": "Enter the category or button description", "emoji_label": "Emoji (Optional)", "emoji_placeholder": "Enter an emoji (e.g., 🎫) or leave empty"}, "send_panel": {"title": "Send Panel to a Channel", "description": "Select a channel to send the panel \"{panel_name}\" into.", "select_placeholder": "Select a channel"}, "ticket_name_format": {"title": "Edit Ticket Name Format", "description": "Choose the ticket name format for the panel \"{panel_name}\"", "username_format": "Username Format", "username_description": "Use the username in the ticket name (e.g., ticket-username)", "numbered_format": "Sequential Numbering", "numbered_description": "Use sequential numbering in the ticket name (e.g., ticket-1)", "current_format": "Current Format", "format_updated": "Ticket name format updated successfully."}}, "ticket": {"create_button": "Create Ticket", "welcome": {"title": "New Support Ticket", "description": "Hello {user},\n\nThank you for creating a support ticket. Our support team will respond to you as soon as possible.\n\nPlease describe your issue or inquiry in detail."}, "close_button": "Close Ticket", "add_member_button": "Add Member", "remove_member_button": "Remove Member", "delete_ticket_button": "Delete Ticket", "create_transcript_button": "Create Transcript", "close_confirmation": {"title": "Confirm Ticket Closure", "description": "Are you sure you want to close this ticket?", "confirm_button": "Confirm", "cancel_button": "Cancel"}, "closing": {"title": "Ticket Closed", "description": "This ticket has been closed. You can now delete it or create a transcript."}, "canceled": {"title": "Ticket Closure Canceled", "description": "The ticket closure process has been canceled."}, "created": "Your support ticket has been created: {channel}", "add_member": {"title": "Add Member to Ticket", "description": "Please select the member you want to add to this ticket.", "input_label": "Member ID", "input_placeholder": "Enter the member ID you want to add", "success": "Successfully added {user} to the ticket."}, "remove_member": {"title": "Remove Member from Ticket", "description": "Please select the member you want to remove from this ticket.", "input_label": "Member ID", "input_placeholder": "Enter the member ID you want to remove", "success": "Successfully removed {user} from the ticket."}, "delete": {"title": "Confirm Ticket Deletion", "description": "Are you sure you want to permanently delete this ticket?", "confirm_button": "Confirm", "cancel_button": "Cancel", "success": "Ticket has been successfully deleted."}, "transcript": {"title": "Ticket Transcript", "description": "A transcript of the ticket has been created and sent to the transcript channel.", "success": "Ticket transcript has been successfully created."}, "log": {"ticket_created": "New Ticket Created", "ticket_closed": "Ticket Closed", "ticket_deleted": "Ticket Deleted", "ticket_reopened": "Ticket Reopened", "reminder_sent": "<PERSON><PERSON><PERSON>", "member_added": "Member Added to Ticket", "member_removed": "Member Removed from Ticket", "created_by": "Created by", "closed_by": "Closed by", "deleted_by": "Deleted by", "reopened_by": "Reopened by", "reminder_by": "Reminder sent by", "added_by": "Added by", "removed_by": "Removed by"}}, "help": {"title": "Ticket AR Help", "description": "Welcome to the Ticket AR help system! Here's a list of available commands:", "commands": {"setup": "Set up the ticket system in your server", "help": "Display this message", "debug": "Debug bot issues in a specific channel", "setlang": "Change the bot language (ar/en)"}, "support_button": "Support"}, "debug": {"title": "Ticket AR Debug", "description": "Diagnosing the bot in channel: {channel}", "connection_status": "Connection Status", "bot_permissions": "Bot Permissions", "permissions_complete": "Complete", "permissions_missing": "Missing", "missing_permissions": "Missing Permissions"}, "setlang": {"description": "Change the bot language", "options": {"language": {"name": "language", "description": "Choose the language (ar for Arabic, en for English)"}}, "success": "Successfully changed the bot language to {language}.", "invalid_language": "Invalid language. Available options are: ar (Arabic), en (English)."}, "errors": {"permission_denied": "You need administrator permissions to use this command.", "setup_error": "There was an error with the setup state. Please start the setup again using the `/setup` command.", "select_channel": "Please select a channel to send the panel to.", "channel_not_found": "The selected channel was not found. Please try again.", "panel_creation_error": "An error occurred while creating the ticket panel. Please try again or use the `/debug` command.", "no_panels": "No panels available. Please create a new panel first.", "panel_not_found": "The selected panel was not found. Please try again.", "delete_error": "An error occurred while deleting the panel. Please try again.", "command_error": "An error occurred while executing this command!", "button_error": "An error occurred while processing this button!", "menu_error": "An error occurred while processing this menu!", "modal_error": "An error occurred while processing this form!", "ticket_creation_error": "An error occurred while creating the ticket. Please try again or contact a server administrator.", "ticket_category_error": "Ticket category not found. Please contact a server administrator.", "invalid_ticket": "This is not a valid ticket channel.", "ticket_close_error": "An error occurred while trying to close the ticket.", "ticket_cancel_error": "An error occurred while trying to cancel the ticket closure.", "ticket_member_add_error": "An error occurred while trying to add a member to the ticket.", "ticket_member_remove_error": "An error occurred while trying to remove a member from the ticket.", "ticket_delete_error": "An error occurred while trying to delete the ticket.", "transcript_error": "An error occurred while trying to create a transcript of the ticket.", "already_has_ticket": "You already have an open ticket. Please use your current ticket or close it before creating a new one.", "invalid_image_url": "Invalid image URL. Please make sure the URL points directly to an image file (jpg, jpeg, png, gif, webp) and from a trusted source.", "image_url_processing_error": "An error occurred while processing the image URL. Please try again with a valid image URL.", "database_error": "An error occurred while updating the database. Please try again.", "no_categories": "No categories/buttons available. Please add a new category/button first.", "category_not_found": "The selected category/button was not found. Please try again.", "category_delete_error": "An error occurred while deleting the category/button. Please try again."}}