const fs = require('fs');
const path = require('path');

console.log('🔍 اختبار ffmpeg...');

try {
  // اختبار require
  const ffmpegPath = require('ffmpeg-static');
  console.log('✅ ffmpeg-static path:', ffmpegPath);
  
  // اختبار وجود الملف
  if (fs.existsSync(ffmpegPath)) {
    console.log('✅ ffmpeg.exe موجود');
    
    // اختبار معلومات الملف
    const stats = fs.statSync(ffmpegPath);
    console.log('📊 حجم الملف:', (stats.size / 1024 / 1024).toFixed(2), 'MB');
    console.log('📅 تاريخ التعديل:', stats.mtime);
    
    // اختبار الصلاحيات
    try {
      fs.accessSync(ffmpegPath, fs.constants.F_OK | fs.constants.R_OK);
      console.log('✅ الملف قابل للقراءة');
    } catch (error) {
      console.error('❌ مشكلة في صلاحيات الملف:', error.message);
    }
    
    // اختبار تشغيل ffmpeg
    const { spawn } = require('child_process');
    console.log('🔄 اختبار تشغيل ffmpeg...');
    
    const ffmpeg = spawn(ffmpegPath, ['-version'], { 
      stdio: ['pipe', 'pipe', 'pipe'],
      timeout: 5000 
    });
    
    let output = '';
    let errorOutput = '';
    
    ffmpeg.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    ffmpeg.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    ffmpeg.on('close', (code) => {
      if (code === 0) {
        console.log('✅ ffmpeg يعمل بشكل صحيح');
        console.log('📋 إصدار ffmpeg:');
        console.log(output.split('\n')[0]);
      } else {
        console.error('❌ فشل في تشغيل ffmpeg، كود الخروج:', code);
        if (errorOutput) {
          console.error('خطأ:', errorOutput);
        }
      }
    });
    
    ffmpeg.on('error', (error) => {
      console.error('❌ خطأ في تشغيل ffmpeg:', error.message);
    });
    
    // timeout للاختبار
    setTimeout(() => {
      if (!ffmpeg.killed) {
        ffmpeg.kill();
        console.log('⏰ انتهت مهلة اختبار ffmpeg');
      }
    }, 10000);
    
  } else {
    console.error('❌ ffmpeg.exe غير موجود في المسار:', ffmpegPath);
    
    // البحث عن ffmpeg في مواقع أخرى
    const possiblePaths = [
      path.join(__dirname, 'node_modules', 'ffmpeg-static', 'ffmpeg.exe'),
      path.join(process.cwd(), 'node_modules', 'ffmpeg-static', 'ffmpeg.exe'),
      path.join(__dirname, '..', '..', 'node_modules', 'ffmpeg-static', 'ffmpeg.exe')
    ];
    
    console.log('🔍 البحث في مواقع أخرى...');
    for (const possiblePath of possiblePaths) {
      if (fs.existsSync(possiblePath)) {
        console.log('✅ وجد ffmpeg في:', possiblePath);
        break;
      } else {
        console.log('❌ لم يوجد في:', possiblePath);
      }
    }
  }
  
} catch (error) {
  console.error('❌ خطأ في اختبار ffmpeg:', error.message);
  
  // محاولة تثبيت ffmpeg-static مرة أخرى
  console.log('🔄 محاولة إعادة تثبيت ffmpeg-static...');
  
  const { spawn } = require('child_process');
  const npm = spawn('npm', ['install', 'ffmpeg-static@^5.2.0', '--force'], {
    stdio: 'inherit',
    shell: true
  });
  
  npm.on('close', (code) => {
    if (code === 0) {
      console.log('✅ تم إعادة تثبيت ffmpeg-static بنجاح');
      
      // اختبار مرة أخرى
      try {
        const newPath = require('ffmpeg-static');
        console.log('✅ المسار الجديد:', newPath);
        
        if (fs.existsSync(newPath)) {
          console.log('✅ ffmpeg متوفر الآن');
        } else {
          console.error('❌ لا يزال ffmpeg غير متوفر');
        }
      } catch (retryError) {
        console.error('❌ فشل في الاختبار بعد إعادة التثبيت:', retryError.message);
      }
    } else {
      console.error('❌ فشل في إعادة تثبيت ffmpeg-static');
    }
  });
}

// معلومات إضافية
console.log('\n📋 معلومات النظام:');
console.log('🖥️ النظام:', process.platform);
console.log('🏗️ المعمارية:', process.arch);
console.log('📦 Node.js:', process.version);
console.log('📁 مجلد العمل:', process.cwd());
console.log('📁 مجلد السكريبت:', __dirname);
