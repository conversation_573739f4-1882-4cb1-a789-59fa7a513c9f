const { MessageFlags } = require('discord.js');

/**
 * انتظار تفاعل مع معالجة أفضل للأخطاء
 * @param {Channel} channel - القناة
 * @param {Object} options - خيارات المجمع
 * @returns {Promise} التفاعل أو خطأ
 */
async function safeAwaitMessageComponent(channel, options = {}) {
  const defaultOptions = {
    time: 60000,
    ...options
  };

  try {
    const interaction = await channel.awaitMessageComponent(defaultOptions);
    return { success: true, interaction };
  } catch (error) {
    console.error('Error in awaitMessageComponent:', error);
    
    if (error.code === 'InteractionCollectorError') {
      return { 
        success: false, 
        error: 'timeout',
        message: 'انتهت مهلة انتظار التفاعل'
      };
    }
    
    return { 
      success: false, 
      error: 'unknown',
      message: error.message || 'حدث خطأ غير معروف'
    };
  }
}

/**
 * إنشاء مجمع تفاعلات آمن
 * @param {Message} message - الرسالة
 * @param {Object} options - خيارات المجمع
 * @returns {InteractionCollector} المجمع
 */
function createSafeCollector(message, options = {}) {
  const defaultOptions = {
    time: 300000, // 5 دقائق
    ...options
  };

  const collector = message.createMessageComponentCollector(defaultOptions);

  // معالجة انتهاء الوقت
  collector.on('end', (collected, reason) => {
    if (reason === 'time' && collected.size === 0) {
      console.log('Collector ended due to timeout with no interactions');
    }
  });

  // معالجة الأخطاء
  collector.on('error', (error) => {
    console.error('Collector error:', error);
  });

  return collector;
}

/**
 * إرسال رسالة خطأ آمنة
 * @param {Interaction} interaction - التفاعل
 * @param {String} message - رسالة الخطأ
 * @param {Boolean} ephemeral - هل الرسالة خاصة
 */
async function safeErrorReply(interaction, message, ephemeral = true) {
  try {
    const replyOptions = {
      content: message,
      flags: ephemeral ? MessageFlags.Ephemeral : undefined
    };

    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply(replyOptions);
    } else if (interaction.deferred) {
      await interaction.editReply({ content: message });
    } else {
      await interaction.followUp(replyOptions);
    }
  } catch (error) {
    console.error('Error sending error reply:', error);
  }
}

/**
 * إرسال رسالة نجاح آمنة
 * @param {Interaction} interaction - التفاعل
 * @param {String} message - رسالة النجاح
 * @param {Boolean} ephemeral - هل الرسالة خاصة
 */
async function safeSuccessReply(interaction, message, ephemeral = true) {
  try {
    const replyOptions = {
      content: message,
      flags: ephemeral ? MessageFlags.Ephemeral : undefined
    };

    if (!interaction.replied && !interaction.deferred) {
      await interaction.reply(replyOptions);
    } else if (interaction.deferred) {
      await interaction.editReply({ content: message });
    } else {
      await interaction.followUp(replyOptions);
    }
  } catch (error) {
    console.error('Error sending success reply:', error);
  }
}

/**
 * تأجيل الرد بشكل آمن
 * @param {Interaction} interaction - التفاعل
 * @param {Boolean} ephemeral - هل الرد خاص
 */
async function safeDeferReply(interaction, ephemeral = true) {
  try {
    if (!interaction.replied && !interaction.deferred) {
      await interaction.deferReply({ 
        flags: ephemeral ? MessageFlags.Ephemeral : undefined 
      });
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error deferring reply:', error);
    return false;
  }
}

/**
 * تحديث التفاعل بشكل آمن
 * @param {Interaction} interaction - التفاعل
 * @param {Object} options - خيارات التحديث
 */
async function safeUpdate(interaction, options) {
  try {
    if (!interaction.replied) {
      await interaction.update(options);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error updating interaction:', error);
    return false;
  }
}

/**
 * معالجة انتهاء وقت المودال
 * @param {Interaction} interaction - التفاعل الأصلي
 * @param {Number} timeout - مهلة الانتظار بالميلي ثانية
 * @returns {Promise} نتيجة المودال
 */
async function safeAwaitModalSubmit(interaction, timeout = 60000) {
  try {
    const modalSubmit = await interaction.awaitModalSubmit({ time: timeout });
    return { success: true, modalSubmit };
  } catch (error) {
    console.error('Error in awaitModalSubmit:', error);
    
    if (error.code === 'InteractionCollectorError') {
      // إرسال رسالة انتهاء الوقت
      try {
        await interaction.followUp({
          content: "⏰ انتهت مهلة إدخال البيانات.",
          flags: MessageFlags.Ephemeral
        });
      } catch (followUpError) {
        console.error('Error sending timeout message:', followUpError);
      }
      
      return { 
        success: false, 
        error: 'timeout',
        message: 'انتهت مهلة إدخال البيانات'
      };
    }
    
    return { 
      success: false, 
      error: 'unknown',
      message: error.message || 'حدث خطأ غير معروف'
    };
  }
}

/**
 * تنظيف المجمعات والتفاعلات
 * @param {Array} collectors - قائمة المجمعات
 */
function cleanupCollectors(collectors = []) {
  collectors.forEach(collector => {
    if (collector && typeof collector.stop === 'function') {
      collector.stop('cleanup');
    }
  });
}

module.exports = {
  safeAwaitMessageComponent,
  createSafeCollector,
  safeErrorReply,
  safeSuccessReply,
  safeDeferReply,
  safeUpdate,
  safeAwaitModalSubmit,
  cleanupCollectors
};
