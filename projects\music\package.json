{"name": "nodejs", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@discordjs/voice": "^0.16.1", "@distube/deezer": "^1.0.0", "@distube/soundcloud": "^1.3.0", "@distube/spotify": "^1.5.1", "axios": "^1.3.2", "discord.js": "^14.7.1", "distube": "^4.0.4", "ffmpeg": "^0.0.4", "ffmpeg-static": "^5.2.0", "libsodium-wrappers": "^0.7.11", "mongoose": "^6.9.1", "node-fetch": "^3.2.6", "opusscript": "^0.0.8", "@discordjs/opus": "^0.9.0", "sodium-native": "^4.0.4", "prism-media": "^1.3.5", "st.db": "^6.0.1", "tweetnacl": "^1.0.3"}}