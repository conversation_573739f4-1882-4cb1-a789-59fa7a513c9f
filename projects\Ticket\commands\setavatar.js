const { SlashCommandBuilder, PermissionFlagsBits, MessageFlags } = require('discord.js');
const https = require('https');

module.exports = {
  data: new SlashCommandBuilder()
    .setName('setavatar')
    .setDescription('Change the bot avatar')
    .addAttachmentOption(option =>
      option.setName('image')
        .setDescription('New avatar image for the bot')
        .setRequired(false)
    )
    .addStringOption(option =>
      option.setName('url')
        .setDescription('URL of the new avatar image')
        .setRequired(false)
    )
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator),

  async execute(interaction, client) {
    try {
      // التحقق من الصلاحيات
      if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
        return await interaction.reply({
          content: '❌ تحتاج إلى صلاحية المدير لاستخدام هذا الأمر.',
          flags: MessageFlags.Ephemeral
        });
      }

      const attachment = interaction.options.getAttachment('image');
      const imageUrl = interaction.options.getString('url');

      if (!attachment && !imageUrl) {
        return await interaction.reply({
          content: '❌ يجب تحديد صورة أو رابط صورة.',
          flags: MessageFlags.Ephemeral
        });
      }

      await interaction.deferReply({ flags: MessageFlags.Ephemeral });

      let avatarUrl = imageUrl;
      
      // إذا تم رفع صورة، استخدم رابطها
      if (attachment) {
        // التحقق من نوع الملف
        const validTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/webp'];
        if (!validTypes.includes(attachment.contentType)) {
          return await interaction.editReply({
            content: '❌ نوع الملف غير مدعوم. يرجى استخدام PNG, JPG, JPEG, GIF, أو WebP.'
          });
        }

        // التحقق من حجم الملف (8MB max)
        if (attachment.size > 8 * 1024 * 1024) {
          return await interaction.editReply({
            content: '❌ حجم الملف كبير جداً. الحد الأقصى هو 8MB.'
          });
        }

        avatarUrl = attachment.url;
      }

      // تحميل الصورة
      const imageBuffer = await downloadImage(avatarUrl);
      
      // تغيير صورة البوت
      await client.user.setAvatar(imageBuffer);

      await interaction.editReply({
        content: `✅ تم تغيير صورة البوت بنجاح!\n\n🖼️ **الصورة الجديدة:** [اضغط هنا لعرضها](${avatarUrl})\n\n⚠️ **ملاحظة:** قد يستغرق التغيير بضع دقائق ليظهر في جميع السيرفرات.`
      });

      console.log(`Bot avatar changed by ${interaction.user.tag} using: ${avatarUrl}`);

    } catch (error) {
      console.error('Error in setavatar command:', error);
      
      let errorMessage = '❌ حدث خطأ أثناء تغيير صورة البوت.';
      
      if (error.code === 50035) {
        errorMessage = '❌ الصورة المدخلة غير صالحة أو تالفة.';
      } else if (error.code === 50013) {
        errorMessage = '❌ البوت لا يملك الصلاحيات اللازمة لتغيير صورته.';
      } else if (error.message.includes('rate limit')) {
        errorMessage = '❌ تم تغيير الصورة مؤخراً. يرجى المحاولة مرة أخرى بعد ساعة.';
      } else if (error.message.includes('Invalid URL') || error.message.includes('fetch')) {
        errorMessage = '❌ لا يمكن تحميل الصورة من الرابط المحدد. تأكد من صحة الرابط.';
      }

      if (interaction.deferred) {
        await interaction.editReply({ content: errorMessage });
      } else {
        await interaction.reply({ content: errorMessage, flags: MessageFlags.Ephemeral });
      }
    }
  },
};

// دالة تحميل الصورة
async function downloadImage(url) {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download image. Status code: ${response.statusCode}`));
        return;
      }

      const data = [];
      response.on('data', chunk => data.push(chunk));
      response.on('end', () => {
        const buffer = Buffer.concat(data);
        
        // التحقق من حجم الصورة
        if (buffer.length > 8 * 1024 * 1024) {
          reject(new Error('Image size too large (max 8MB)'));
          return;
        }
        
        resolve(buffer);
      });
    }).on('error', reject);
  });
}
