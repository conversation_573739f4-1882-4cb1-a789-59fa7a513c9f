const mongoose = require('mongoose');
require('dotenv').config();

// اتصال بقاعدة البيانات MongoDB Atlas
const connectDB = async () => {
  try {
    // التحقق من وجود MONGODB_URI
    const mongoUri = process.env.MONGODB_URI || 'mongodb+srv://whm:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

    if (!mongoUri || mongoUri === 'undefined') {
      console.error('MONGODB_URI is not defined in environment variables');
      return false;
    }

    console.log('Connecting to MongoDB with URI:', mongoUri.substring(0, 20) + '...');

    const conn = await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      serverSelectionTimeoutMS: 30000, // زيادة timeout لتجنب المشاكل
      connectTimeoutMS: 30000,
      socketTimeoutMS: 45000, // إضافة socket timeout
      maxPoolSize: 10, // تحديد حجم connection pool
      minPoolSize: 5,
      maxIdleTimeMS: 30000,
      waitQueueTimeoutMS: 5000,
    });

    console.log(`MongoDB Connected: ${conn.connection.host}`);
    return true;
  } catch (error) {
    console.error(`Error connecting to MongoDB: ${error.message}`);
    return false;
  }
};

module.exports = { connectDB };
