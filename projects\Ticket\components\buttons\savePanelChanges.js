const { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON><PERSON>er, <PERSON>ton<PERSON><PERSON>er, ButtonStyle, StringSelectMenuBuilder, MessageFlags } = require('discord.js');
const { setupStates } = require('../buttons');
const { panelService } = require('../../services');

module.exports = {
  id: 'save_panel_changes',

  async execute(interaction, client) {
    const state = setupStates.get(interaction.user.id);

    if (!state) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.setup_error');
      return interaction.reply({
        content: errorMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // تأجيل الرد على التفاعل لزيادة مهلة الاستجابة
    await interaction.deferUpdate();

    // الحصول على الترجمات
    const ticketDescription = await client.translate(interaction.guild.id, 'ticket.create_button');

    // الحصول على بيانات اللوحة المحدثة من قاعدة البيانات
    const panel = await panelService.getPanelById(state.selectedPanel);

    if (!panel) {
      const errorMessage = await client.translate(interaction.guild.id, 'errors.panel_not_found');
      return interaction.editReply({
        content: errorMessage
      });
    }

    // تحديث تنسيق اسم التذكرة إذا تم تغييره
    if (state.ticketNameFormat) {
      await panelService.updatePanel(state.selectedPanel, { ticketNameFormat: state.ticketNameFormat });

      // إرسال رسالة تأكيد
      const formatUpdatedMessage = await client.translate(interaction.guild.id, 'panel_editor.ticket_name_format.format_updated');
      await interaction.followUp({
        content: formatUpdatedMessage,
        flags: MessageFlags.Ephemeral
      });
    }

    // تحديث اللوحة المرسلة في القناة إذا كانت موجودة
    if (panel.messageId && panel.channelId) {
      try {
        // الحصول على القناة والرسالة
        const channel = await interaction.guild.channels.fetch(panel.channelId).catch(() => null);

        if (channel) {
          const message = await channel.messages.fetch(panel.messageId).catch(() => null);

          if (message) {
            // إنشاء مكونات اللوحة المحدثة
            let row;

            // إنشاء قائمة منسدلة أو أزرار حسب نوع العرض
            if (panel.displayType.startsWith('dropdown')) {
              // إنشاء قائمة منسدلة
              const selectMenu = new StringSelectMenuBuilder()
                .setCustomId(`create_ticket_${panel.panelId}`)
                .setPlaceholder(ticketDescription);

              // إضافة الفئات إلى القائمة المنسدلة
              if (panel.categories && panel.categories.length > 0) {
                panel.categories.forEach(category => {
                  // إنشاء خيار جديد بدون رموز تعبيرية
                  selectMenu.addOptions({
                    label: category.name,
                    value: `ticket_category_${panel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`,
                    description: category.description || 'إنشاء تذكرة دعم'
                  });
                });
              } else {
                // إضافة خيار افتراضي إذا لم تكن هناك فئات
                selectMenu.addOptions({
                  label: 'تذكرة عامة',
                  value: `create_ticket_${panel.panelId}`,
                  description: 'إنشاء تذكرة دعم عامة'
                });
              }

              row = new ActionRowBuilder().addComponents(selectMenu);
            } else {
              // إنشاء أزرار
              row = new ActionRowBuilder();

              // إضافة الفئات كأزرار
              if (panel.categories && panel.categories.length > 0) {
                panel.categories.forEach(category => {
                  if (row.components.length < 5) { // الحد الأقصى هو 5 أزرار في صف واحد
                    // إنشاء زر جديد بدون رموز تعبيرية
                    row.addComponents(
                      new ButtonBuilder()
                        .setCustomId(`create_ticket_${panel.panelId}_${category.name.replace(/\s+/g, '_').toLowerCase()}`)
                        .setLabel(category.name)
                        .setStyle(ButtonStyle.Primary)
                    );
                  }
                });
              } else {
                // إضافة زر افتراضي إذا لم تكن هناك فئات
                row.addComponents(
                  new ButtonBuilder()
                    .setCustomId(`create_ticket_${panel.panelId}`)
                    .setLabel(ticketDescription)
                    .setStyle(ButtonStyle.Primary)
                );
              }
            }

            // تحديث الرسالة حسب نوع العرض
            if (panel.displayType === 'dropdown_image' || panel.displayType === 'buttons_image') {
              if (panel.imageUrl) {
                // تحديث الرسالة مع الصورة
                await message.edit({
                  content: null,
                  embeds: [],
                  files: [panel.imageUrl],
                  components: [row]
                });
              } else {
                // إذا لم تكن هناك صورة، استخدم إمبد بدلاً من ذلك
                const panelEmbed = new EmbedBuilder()
                  .setColor(client.config.embedColor)
                  .setTitle(panel.name)
                  .setDescription(panel.description)
                  .setFooter({ text: client.config.footerText });

                await message.edit({
                  content: null,
                  embeds: [panelEmbed],
                  files: [],
                  components: [row]
                });
              }
            } else {
              // تحديث الرسالة مع إمبد
              const panelEmbed = new EmbedBuilder()
                .setColor(client.config.embedColor)
                .setTitle(panel.name)
                .setDescription(panel.description)
                .setFooter({ text: client.config.footerText });

              await message.edit({
                content: null,
                embeds: [panelEmbed],
                files: [],
                components: [row]
              });
            }
          }
        }
      } catch (error) {
        console.error('Error updating panel message:', error);
      }
    }

    // الحصول على الترجمات
    const title = await client.translate(interaction.guild.id, 'setup.title');
    const description = await client.translate(interaction.guild.id, 'setup.description');
    const helpText = await client.translate(interaction.guild.id, 'setup.help_text');
    const editPanelsButton = await client.translate(interaction.guild.id, 'setup.edit_panels_button');
    const createPanelButton = await client.translate(interaction.guild.id, 'setup.create_panel_button');
    const changesSuccessMessage = await client.translate(interaction.guild.id, 'panel_editor.changes_saved');

    // إعادة عرض واجهة الإعداد الأساسية
    const embed = new EmbedBuilder()
      .setColor(client.config.embedColor)
      .setTitle(title)
      .setDescription(description)
      .addFields(
        { name: '\u200B', value: helpText }
      )
      .setImage('https://media.discordapp.net/attachments/1117593323167830107/1370364076915687484/Ticket_AR_1.png?ex=681f3a80&is=681de900&hm=3e983b2d29c39c6354b4026eeaf973d0c7b86001cc7bf650e63268abbe3bed35&=&format=webp&quality=lossless&width=1529&height=813'); // استبدل هذا برابط صورة مناسبة

    const row = new ActionRowBuilder()
      .addComponents(
        new ButtonBuilder()
          .setCustomId('edit_panels')
          .setLabel(editPanelsButton)
          .setStyle(ButtonStyle.Primary)
          .setEmoji('📋'),
        new ButtonBuilder()
          .setCustomId('create_panel')
          .setLabel(createPanelButton)
          .setStyle(ButtonStyle.Success)
          .setEmoji('➕')
      );

    await interaction.editReply({ embeds: [embed], components: [row] });

    // إرسال رسالة تأكيد
    await interaction.followUp({
      content: changesSuccessMessage,
      flags: MessageFlags.Ephemeral
    });

    // إعادة تعيين حالة المستخدم
    state.selectedPanel = null;
    state.selectedAction = null;
    setupStates.set(interaction.user.id, state);
  }
};
