const { Database } = require('st.db');
const db = new Database({
  path: "databases/subscriptions.json",
  autoSave: true,
  saveTimeout: 0,
  encryption: false
});
const tokens_db = new Database({
  path: "databases/tokens.json",
  autoSave: true,
  saveTimeout: 0,
  encryption: false
});
const pm2 = require("pm2");
const ms = require("ms");
const pm2_cooldown = 1000;
const { Client, GatewayIntentBits, Partials, EmbedBuilder } = require("discord.js");
const Discord = require("discord.js");
const client = new Client({ partials: [Partials.Message, Partials.Channel, Partials.User, Partials.GuildMember], intents: [GatewayIntentBits.Guilds, GatewayIntentBits.GuildMembers, GatewayIntentBits.MessageContent, GatewayIntentBits.GuildMessages] });
const config = require("./config");
const prefix = config.prefix;
const fetch = (...args) => import('node-fetch').then(({ default: fetch }) => fetch(...args));
const axios = require("axios");
const Ms = require("pretty-ms");
const canvas = require("canvas");
const prettyMilliseconds = require('pretty-ms');
const fs = require('fs');

client.on("ready", async () => {
  console.log(client.user.tag);
  try {
    // قراءة ملف الاشتراكات مباشرة للتأكد من الحصول على البيانات الصحيحة
    const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

    console.log(`Loaded subscriptions data from file. Format:`, typeof subsData);

    // إعادة تحميل قاعدة البيانات في الذاكرة
    await db.set("subscriptions", subsData);

    // التعامل مع تنسيقات البيانات المختلفة
    if (subsData && typeof subsData === 'object') {
      let subscriptionsCount = 0;
      let activeSubscriptionsCount = 0;

      // طباعة محتويات الملف للتصحيح
      console.log("Subscriptions file content sample:", JSON.stringify(subsData).substring(0, 200) + "...");

      // إذا كان التنسيق هو كائن مع مفاتيح (تنسيق جديد)
      if (!Array.isArray(subsData)) {
        for (const [userID, userData] of Object.entries(subsData)) {
          if (Array.isArray(userData)) {
            subscriptionsCount += userData.length;
            // تعديل شرط الاشتراكات النشطة: التحقق من تاريخ الانتهاء وحقل ended
            const activeData = userData.filter(d => d.endDate > Date.now() && d.ended !== true);
            activeSubscriptionsCount += activeData.length;

            console.log(`User ${userID} has ${userData.length} subscriptions, ${activeData.length} active`);

            for (let sub of activeData) {
              console.log(`Processing subscription for user ${userID}, subID: ${sub.id}, endDate: ${new Date(sub.endDate)}, ended: ${sub.ended}`);
              try {
                await runSub(userID, sub);
              } catch (subError) {
                console.error(`Error running subscription for user ${userID}, subID: ${sub.id}:`, subError);
              }
            }
          }
        }
      }
      // إذا كان التنسيق هو مصفوفة من الكائنات (تنسيق قديم)
      else if (Array.isArray(subsData)) {
        for (let data of subsData) {
          if (data && data.data && Array.isArray(data.data)) {
            subscriptionsCount += data.data.length;
            // تعديل شرط الاشتراكات النشطة: التحقق من تاريخ الانتهاء وحقل ended
            const activeData = data.data.filter(d => d.endDate > Date.now() && d.ended !== true);
            activeSubscriptionsCount += activeData.length;

            for (let sub of activeData) {
              console.log(`Processing subscription for user ${data.ID}, subID: ${sub.id}`);
              try {
                await runSub(data.ID, sub);
              } catch (subError) {
                console.error(`Error running subscription for user ${data.ID}, subID: ${sub.id}:`, subError);
              }
            }
          }
        }
      }

      console.log(`Total subscriptions: ${subscriptionsCount}, Active subscriptions: ${activeSubscriptionsCount}`);

      // تحديث قاعدة البيانات في الذاكرة
      await db.set("subscriptions", subsData);
    } else {
      console.error("Unexpected format from subscriptions file:", subsData);
    }

    console.log("Bot is fully ready and subscriptions are loaded!");
  } catch (error) {
    console.error("Error during startup:", error);
  }
});

client.on("messageCreate", async (message) => {
  if (message.author.bot || !message.guild) return;
  let args = message.content.split(" ");
  if (args[0] === prefix + "add") {
    if (!config.devs.find(u => u === message.author.id)) return;
    let owner = args[1] ? message.mentions.users.first() || await client.users.fetch(args[1]).catch(() => 0) : null;
    if (!owner) return message.reply({ content: `❌ Please provide valid user` });
    let guildID = args[2];
    let time = args[3] ? ms(args[3]) : null;
    if (!guildID || isNaN(guildID)) return message.channel.send({ content: `❌ Please provide valid guildID` });
    if (!time || isNaN(time)) return message.channel.send({ content: `❌ Please provide valid time` });
    let startDate = Date.now();
    let endDate = Date.now() + time;
    let row = new Discord.ActionRowBuilder()
      .addComponents(
        new Discord.SelectMenuBuilder()
          .setCustomId(`choose_types`)
          .setPlaceholder('Choose subscriptions')
          .setMinValues(1)
          .setMaxValues(config.types.length)
          .addOptions(config.types.map(t => ({
            label: t,
            value: t
          }))),
      );
    let msg = await message.reply({ content: `Choose bots type to complete order`, components: [row] });
    let collect = await msg.awaitMessageComponent({ filter: m => m.user.id === message.author.id && m.customId === "choose_types" }).catch(() => 0);
    if (!collect || !collect.customId) return;
    collect.deferUpdate().catch(() => 0);
    let types = collect.values;
    // قراءة التوكنات مباشرة من الملف
    const fs = require('fs');
    let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
    let tokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

    console.log(`Current tokens in stock: ${tokens.length}`);

    if (!tokens || tokens.length < types.length) return msg.edit({ content: `❌ you don't have enough tokens in stock.`, components: [] });

    // أخذ التوكنات المطلوبة
    let bots_tokens = tokens.slice(0, types.length);
    for (let i = 0; i < bots_tokens.length; i++) {
      bots_tokens[i].type = types[i];
    }

    // تحديث قائمة التوكنات
    tokens = tokens.slice(types.length);
    console.log(`Tokens after removal: ${tokens.length}`);

    // تحديث الملف مباشرة
    tokensData.tokens = tokens;
    fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

    // تحديث قاعدة البيانات في الذاكرة
    await tokens_db.set("tokens", tokens);
    let sub_id = Date.now().toString(16);
    let sub_data = {
      id: sub_id,
      bots: bots_tokens,
      startDate,
      endDate,
      guildID,
      ended: false
    }
    // إضافة الاشتراك إلى قاعدة البيانات
    await db.push(owner.id, sub_data);

    // تحديث ملف الاشتراكات مباشرة
    try {
      const fs = require('fs');
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

      // إضافة الاشتراك إلى المستخدم
      if (!subsData[owner.id]) {
        subsData[owner.id] = [];
      }
      subsData[owner.id].push(sub_data);

      // حفظ الملف
      fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
      console.log(`Subscription ${sub_data.id} added to file for user ${owner.id}`);
    } catch (error) {
      console.error(`Error updating subscriptions file:`, error);
    }

    // إعطاء الرتبة للمستخدم بناءً على نوع الاشتراك
    try {
      const guild = client.guilds.cache.get(message.guild.id);
      if (guild) {
        const member = await guild.members.fetch(owner.id).catch(() => null);
        if (member) {
          // رتبة الاشتراك العادي
          const generalRoleId = "1118122686376329276";
          const generalRole = guild.roles.cache.get(generalRoleId);

          if (generalRole && !member.roles.cache.has(generalRoleId)) {
            await member.roles.add(generalRole);
            console.log(`Added general subscription role to user ${owner.id}`);
          }
        }
      }
    } catch (error) {
      console.error("Error adding role to user:", error);
    }

    msg.edit({ content: `✅ Done complete this subscription, bots will be online after some seconds\nuse \`${prefix}links ${args[1]} ${sub_data.id}\` to get bot links`, components: [] }).catch(() => 0);
    let log_ch = client.channels.cache.get(config.log_channel);
    if (log_ch) {
      let embed = new EmbedBuilder()
        .setColor("Green")
        .setTitle("Subscription Start")
        .addFields([
          {
            name: `Subscription  ID:`,
            value: `${sub_data.id}`,
          },
          {
            name: `Author:`,
            value: `<@${owner.id}> (ID: ${owner.id})`
          },
          {
            name: `Bots count:`,
            value: `${sub_data.bots.length} bot`
          },
          {
            name: `Subscription type:`,
            value: `${sub_data.bots.find(e => e.type?.toLowerCase() == "music") ? "music" : sub_data.bots.map(t => t.type).join(", ")}`
          },
          {
            name: `Subscription Start At:`,
            value: `<t:${Math.floor(sub_data.startDate / 1000)}:R>`
          },
          {
            name: `Subscription duration:`,
            value: `${ms(Math.floor(sub_data.endDate - sub_data.startDate))}`
          },
          {
            name: "Guild ID:",
            value: `${sub_data.guildID}`
          }
        ]);
      log_ch.send({ embeds: [embed] });
    }
    let links = sub_data.bots.map(b => `https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${sub_data.guildID}&disable_guild_select=true`);
    let embed_info = new EmbedBuilder()
      .setColor("Green")
      .setTitle("Subscription Start")
      .addFields([
        {
          name: `Subscription  ID:`,
          value: `${sub_data.id}`,
        },
        {
          name: `Bots count:`,
          value: `${sub_data.bots.length} bot`
        },
        {
          name: `Subscription type:`,
          value: `${sub_data.bots.find(e => e.type?.toLowerCase() == "music") ? "music" : sub_data.bots.map(t => t.type).join(", ")}`
        },
        {
          name: `Subscription Start At:`,
          value: `<t:${Math.floor(sub_data.startDate / 1000)}:R>`
        },
        {
          name: `Subscription duration:`,
          value: `${ms(Math.floor(sub_data.endDate - sub_data.startDate))}`
        },
        {
          name: "Guild ID:",
          value: `${sub_data.guildID}`
        }
      ]);
    await owner.send({ embeds: [embed_info] }).catch(() => 0);
    let n = 10;
    for (let i = 0; i < links.length; i += n) {
      let ee = links.slice(i, i + n);
      owner.send({ content: `${ee.join("\n")}` }).catch(() => 0);
    }
    runSub(owner.id, sub_data);
  } else if (args[0] === prefix + "add-music") {
    if (!config.devs.find(u => u === message.author.id)) return;
    let owner = args[1] ? message.mentions.users.first() || await client.users.fetch(args[1]).catch(() => 0) : null;
    if (!owner) return message.reply({ content: `❌ Please provide valid user` });
    let guildID = args[2];
    let time = args[3] ? ms(args[3]) : null;
    let count = args[4];
    if (!guildID || isNaN(guildID)) return message.channel.send({ content: `❌ Please provide valid guildID` });
    if (!time || isNaN(time)) return message.channel.send({ content: `❌ Please provide valid time` });
    let startDate = Date.now();
    let endDate = Date.now() + time;
    if (!count || isNaN(count) || parseInt(count) < 1) return message.reply({ content: `❌ Please provide valid count` });
    count = parseInt(count);
    // قراءة التوكنات مباشرة من الملف
    const fs = require('fs');
    let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
    let tokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

    console.log(`Current tokens in stock: ${tokens.length}`);

    if (!tokens || tokens.length < count) return message.channel.send({ content: `❌ you don't have enough tokens in stock.`, components: [] });

    // أخذ التوكنات المطلوبة
    let bots_tokens = tokens.slice(0, count).map(t => ({ ...t, type: "music" }));

    // تحديث قائمة التوكنات
    tokens = tokens.slice(count);
    console.log(`Tokens after removal: ${tokens.length}`);

    // تحديث الملف مباشرة
    tokensData.tokens = tokens;
    fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

    // تحديث قاعدة البيانات في الذاكرة
    await tokens_db.set("tokens", tokens);
    let sub_id = Date.now().toString(16);
    let sub_data = {
      id: sub_id,
      bots: bots_tokens,
      startDate,
      endDate,
      guildID,
      type: "music",
      ended: false
    }
    // إضافة الاشتراك إلى قاعدة البيانات
    await db.push(owner.id, sub_data);

    // تحديث ملف الاشتراكات مباشرة
    try {
      const fs = require('fs');
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

      // إضافة الاشتراك إلى المستخدم
      if (!subsData[owner.id]) {
        subsData[owner.id] = [];
      }
      subsData[owner.id].push(sub_data);

      // حفظ الملف
      fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
      console.log(`Subscription ${sub_data.id} added to file for user ${owner.id}`);
    } catch (error) {
      console.error(`Error updating subscriptions file:`, error);
    }

    // إعطاء الرتبة للمستخدم بناءً على نوع الاشتراك (موسيقى)
    try {
      const guild = client.guilds.cache.get(message.guild.id);
      if (guild) {
        const member = await guild.members.fetch(owner.id).catch(() => null);
        if (member) {
          // رتبة اشتراك الموسيقى
          const musicRoleId = "1118261695261982773";
          const musicRole = guild.roles.cache.get(musicRoleId);

          if (musicRole && !member.roles.cache.has(musicRoleId)) {
            await member.roles.add(musicRole);
            console.log(`Added music subscription role to user ${owner.id}`);
          }
        }
      }
    } catch (error) {
      console.error("Error adding music role to user:", error);
    }

    message.channel.send({ content: `✅ Done complete this subscription, bots will be online after some seconds\nuse \`${prefix}links ${args[1]} ${sub_data.id}\` to get bot links`, components: [] }).catch(() => 0);
    let log_ch = client.channels.cache.get(config.log_channel);
    if (log_ch) {
      let embed = new EmbedBuilder()
        .setColor("Green")
        .setTitle("Subscription Start")
        .addFields([
          {
            name: `Subscription  ID:`,
            value: `${sub_data.id}`,
          },
          {
            name: `Author:`,
            value: `<@${owner.id}> (ID: ${owner.id})`
          },
          {
            name: `Bots count:`,
            value: `${sub_data.bots.length} bot`
          },
          {
            name: `Subscription type:`,
            value: `${sub_data.bots.find(e => e.type?.toLowerCase() == "music") ? "music" : sub_data.bots.map(t => t.type).join(", ")}`
          },
          {
            name: `Subscription Start At:`,
            value: `<t:${Math.floor(sub_data.startDate / 1000)}:R>`
          },
          {
            name: `Subscription duration:`,
            value: `${ms(Math.floor(sub_data.endDate - sub_data.startDate))}`
          },
          {
            name: "Guild ID:",
            value: `${sub_data.guildID}`
          }
        ]);
      log_ch.send({ embeds: [embed] });
    }
    let embed_info = new EmbedBuilder()
      .setColor("Green")
      .setTitle("Subscription Start")
      .addFields([
        {
          name: `Subscription  ID:`,
          value: `${sub_data.id}`,
        },
        {
          name: `Bots count:`,
          value: `${sub_data.bots.length} bot`
        },
        {
          name: `Subscription type:`,
          value: `${sub_data.bots.find(e => e.type?.toLowerCase() == "music") ? "music" : sub_data.bots.map(t => t.type).join(", ")}`
        },
        {
          name: `Subscription Start At:`,
          value: `<t:${Math.floor(sub_data.startDate / 1000)}:R>`
        },
        {
          name: `Subscription duration:`,
          value: `${ms(Math.floor(sub_data.endDate - sub_data.startDate))}`
        },
        {
          name: "Guild ID:",
          value: `${sub_data.guildID}`
        }
      ]);
    await owner.send({ embeds: [embed_info] }).catch(() => 0);

    // إرسال الروابط بالطريقة القديمة مع إضافة رقم لكل رابط
    let links = sub_data.bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${sub_data.guildID}&disable_guild_select=true`);
    let n = 10;
    for (let i = 0; i < links.length; i += n) {
      let ee = links.slice(i, i + n);
      owner.send({ content: `${ee.join("\n")}` }).catch(() => 0);
    }
    runSub(owner.id, sub_data);
  } else if (args[0] === prefix + "end") {
    if (!config.devs.find(u => u === message.author.id)) return;
    let user = args[1] ? message.mentions.users.first() || await client.users.fetch(args[1]).catch(() => 0) : null;
    if (!user) return message.reply({ content: `❌ Please provide valid user` });
    let sub_id = args[2];
    if (!sub_id) return message.reply({ content: `❌ Please provide subscription id` });
    let data = await db.get(user.id);
    let subscription = data?.find(s => s.id == sub_id);
    let sub_index = data?.findIndex(s => s.id == sub_id);
    if (!subscription || sub_index == -1) return message.reply({ content: `❌ I can't find this subscription` });
    if (subscription.endDate < Date.now()) return message.reply({ content: `❌ Subscription already ended before` });
    endSub(user.id, subscription);
    subscription.endDate = Date.now();
    subscription.ended = true;
    data[sub_index] = subscription;

    // تحديث قاعدة البيانات في الذاكرة
    await db.set(user.id, data);

    // تحديث ملف الاشتراكات مباشرة
    try {
      const fs = require('fs');
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

      // تحديث الاشتراك للمستخدم
      if (subsData[user.id]) {
        const subIndex = subsData[user.id].findIndex(s => s.id === sub_id);
        if (subIndex !== -1) {
          subsData[user.id][subIndex] = subscription;

          // حفظ الملف
          fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
          console.log(`Subscription ${sub_id} ended in file for user ${user.id}`);
        } else {
          console.error(`Subscription ${sub_id} not found in file for user ${user.id}`);
        }
      } else {
        console.error(`User ${user.id} not found in subscriptions file`);
      }
    } catch (error) {
      console.error(`Error updating subscriptions file:`, error);
    }

    message.reply({ content: `✅ Done end this subscription` });
  } else if (args[0] === prefix + "renew") {
    if (!config.devs.find(u => u === message.author.id)) return;
    let user = args[1] ? message.mentions.users.first() || await client.users.fetch(args[1]).catch(() => 0) : null;
    if (!user) return message.reply({ content: `❌ Please provide valid user` });
    let sub_id = args[2];
    if (!sub_id) return message.reply({ content: `❌ Please provide subscription id` });
    let time = args[3] ? ms(args[3]) : null;
    if (!time || isNaN(time)) return message.reply({ content: `❌ Please provide valid time` });
    let data = await db.get(user.id);
    let subscription = data?.find(s => s.id == sub_id);
    let sub_index = data?.findIndex(s => s.id == sub_id);
    if (!subscription || sub_index == -1) return message.reply({ content: `❌ I can't find this subscription` });
    if (subscription.endDate > Date.now()) return message.reply({ content: `😒 Subscription already working now` });
    subscription.startDate = Date.now();
    subscription.endDate = Date.now() + time;
    subscription.ended = false;
    data[sub_index] = subscription;

    // تحديث قاعدة البيانات في الذاكرة
    await db.set(user.id, data);

    // تحديث ملف الاشتراكات مباشرة
    try {
      const fs = require('fs');
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

      // تحديث الاشتراك للمستخدم
      if (subsData[user.id]) {
        const subIndex = subsData[user.id].findIndex(s => s.id === sub_id);
        if (subIndex !== -1) {
          subsData[user.id][subIndex] = subscription;

          // حفظ الملف
          fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
          console.log(`Subscription ${sub_id} renewed in file for user ${user.id}`);
        } else {
          console.error(`Subscription ${sub_id} not found in file for user ${user.id}`);
        }
      } else {
        console.error(`User ${user.id} not found in subscriptions file`);
      }
    } catch (error) {
      console.error(`Error updating subscriptions file:`, error);
    }

    message.reply({ content: `✅ Done renew this subscription, bots will be online after some seconds` });
    let log_ch = client.channels.cache.get(config.log_channel);
    if (log_ch) {
      let embed = new EmbedBuilder()
        .setColor("Gold")
        .setTitle("Subscription Renew")
        .addFields([
          {
            name: `Subscription  ID:`,
            value: `${subscription.id}`,
          },
          {
            name: `Author:`,
            value: `<@${user.id}> (ID: ${user.id})`
          },
          {
            name: `Bots count:`,
            value: `${subscription.bots.length} bot`
          },
          {
            name: `Subscription type:`,
            value: `${subscription.bots.find(e => e.type?.toLowerCase() == "music") ? "music" : subscription.bots.map(t => t.type).join(", ")}`
          },
          {
            name: `Subscription Renewed At:`,
            value: `<t:${Math.floor(subscription.startDate / 1000)}:R>`
          },
          {
            name: `Subscription duration:`,
            value: `${ms(Math.floor(subscription.endDate - subscription.startDate))}`
          },
          {
            name: "Guild ID:",
            value: `${subscription.guildID}`
          }
        ]);
      log_ch.send({ embeds: [embed] });
    }
    runSub(user.id, subscription);
  } else if (args[0] === prefix + "delete") {
    if (!config.devs.find(u => u === message.author.id)) return;

    // إذا كان الخيار هو حذف جميع الاشتراكات غير النشطة لجميع المستخدمين
    if (args[1] === "all-inactive") {
      // إنشاء رسالة تأكيد
      const confirmEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("⚠️ تأكيد حذف جميع الاشتراكات غير النشطة")
        .setDescription("هل أنت متأكد من أنك تريد حذف جميع الاشتراكات غير النشطة لجميع المستخدمين؟")
        .setFooter({ text: "سيتم إلغاء العملية بعد 30 ثانية" });

      const row = new Discord.ActionRowBuilder()
        .addComponents(
          new Discord.ButtonBuilder()
            .setCustomId("confirm_delete_all_inactive")
            .setLabel("تأكيد")
            .setStyle(Discord.ButtonStyle.Danger),
          new Discord.ButtonBuilder()
            .setCustomId("cancel_delete")
            .setLabel("إلغاء")
            .setStyle(Discord.ButtonStyle.Secondary)
        );

      const confirmMsg = await message.reply({ embeds: [confirmEmbed], components: [row] });

      // إنشاء مجمع للأزرار
      const collector = confirmMsg.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: 30000
      });

      collector.on("collect", async (interaction) => {
        if (interaction.customId === "confirm_delete_all_inactive") {
          await interaction.deferUpdate();

          // تحديث الرسالة لإظهار أن العملية قيد التنفيذ
          const processingEmbed = new Discord.EmbedBuilder()
            .setColor("Blue")
            .setTitle("🔄 جاري حذف الاشتراكات غير النشطة...")
            .setDescription("يرجى الانتظار قليلاً حتى تكتمل العملية.");

          await confirmMsg.edit({ embeds: [processingEmbed], components: [] });

          try {
            // قراءة ملف الاشتراكات مباشرة
            const fs = require('fs');
            const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

            if (!subsData || typeof subsData !== 'object') {
              return confirmMsg.edit({
                embeds: [
                  new Discord.EmbedBuilder()
                    .setColor("Red")
                    .setTitle("❌ خطأ")
                    .setDescription("حدث خطأ في قراءة ملف الاشتراكات")
                ]
              });
            }

            let totalDeleted = 0;
            let totalTokensRecovered = 0;
            let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
            let currentTokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

            // معالجة كل مستخدم
            for (const [userID, userData] of Object.entries(subsData)) {
              if (Array.isArray(userData) && userData.length > 0) {
                // الحصول على الاشتراكات غير النشطة
                const inactiveSubscriptions = userData.filter(sub =>
                  sub.endDate <= Date.now() || sub.ended === true
                );

                if (inactiveSubscriptions.length > 0) {
                  // استرجاع التوكنات من الاشتراكات غير النشطة
                  for (const sub of inactiveSubscriptions) {
                    if (sub.bots && Array.isArray(sub.bots)) {
                      // إيقاف البوتات إذا كانت لا تزال تعمل
                      if (sub.endDate > Date.now()) {
                        endSub(userID, sub);
                      }

                      // استرجاع التوكنات
                      let tokens = sub.bots;
                      for (let i = 0; i < tokens.length; i++) {
                        let obj = tokens[i];
                        delete obj["type"];
                        currentTokens.push(obj);
                        totalTokensRecovered++;
                      }
                    }
                  }

                  // حذف الاشتراكات غير النشطة من مصفوفة المستخدم
                  subsData[userID] = userData.filter(sub =>
                    sub.endDate > Date.now() && sub.ended !== true
                  );

                  totalDeleted += inactiveSubscriptions.length;

                  // تحديث قاعدة البيانات في الذاكرة
                  await db.set(userID, subsData[userID]);
                }
              }
            }

            // حفظ ملف الاشتراكات
            fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));

            // تحديث ملف التوكنات
            tokensData.tokens = currentTokens;
            fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

            // تحديث قاعدة البيانات في الذاكرة
            await tokens_db.set("tokens", currentTokens);

            // إنشاء رسالة النجاح
            const successEmbed = new Discord.EmbedBuilder()
              .setColor("Green")
              .setTitle("✅ تم حذف الاشتراكات غير النشطة")
              .setDescription(`تم حذف ${totalDeleted} اشتراك غير نشط واسترجاع ${totalTokensRecovered} توكن إلى المخزون.`)
              .setTimestamp();

            await confirmMsg.edit({ embeds: [successEmbed] });

          } catch (error) {
            console.error("Error deleting all inactive subscriptions:", error);

            const errorEmbed = new Discord.EmbedBuilder()
              .setColor("Red")
              .setTitle("❌ خطأ")
              .setDescription("حدث خطأ أثناء حذف الاشتراكات غير النشطة.")
              .setFooter({ text: error.message });

            await confirmMsg.edit({ embeds: [errorEmbed], components: [] });
          }
        } else if (interaction.customId === "cancel_delete") {
          await interaction.deferUpdate();

          const cancelEmbed = new Discord.EmbedBuilder()
            .setColor("Grey")
            .setTitle("تم إلغاء العملية")
            .setDescription("تم إلغاء عملية حذف الاشتراكات غير النشطة.");

          await confirmMsg.edit({ embeds: [cancelEmbed], components: [] });
        }
      });

      collector.on("end", (collected, reason) => {
        if (reason === "time" && collected.size === 0) {
          const timeoutEmbed = new Discord.EmbedBuilder()
            .setColor("Grey")
            .setTitle("انتهت المهلة")
            .setDescription("انتهت مهلة التأكيد. تم إلغاء عملية الحذف.");

          confirmMsg.edit({ embeds: [timeoutEmbed], components: [] }).catch(() => { });
        }
      });

      return;
    }

    let user = args[1] ? message.mentions.users.first() || await client.users.fetch(args[1]).catch(() => 0) : null;
    if (!user) return message.reply({ content: `❌ Please provide valid user` });

    // إذا كان الخيار هو حذف جميع الاشتراكات غير النشطة للمستخدم
    if (args[2] === "inactive") {
      let data = await db.get(user.id);
      if (!data || data.length < 1) return message.reply({ content: `❌ This user doesn't have any subscriptions` });

      // الحصول على الاشتراكات غير النشطة
      const inactiveSubscriptions = data.filter(sub =>
        sub.endDate <= Date.now() || sub.ended === true
      );

      if (inactiveSubscriptions.length === 0) {
        return message.reply({ content: `❌ This user doesn't have any inactive subscriptions` });
      }

      // إنشاء رسالة تأكيد
      const confirmEmbed = new Discord.EmbedBuilder()
        .setColor("Orange")
        .setTitle("⚠️ تأكيد حذف الاشتراكات غير النشطة")
        .setDescription(`هل أنت متأكد من أنك تريد حذف ${inactiveSubscriptions.length} اشتراك غير نشط للمستخدم <@${user.id}>؟`)
        .setFooter({ text: "سيتم إلغاء العملية بعد 30 ثانية" });

      const row = new Discord.ActionRowBuilder()
        .addComponents(
          new Discord.ButtonBuilder()
            .setCustomId("confirm_delete_inactive")
            .setLabel("تأكيد")
            .setStyle(Discord.ButtonStyle.Danger),
          new Discord.ButtonBuilder()
            .setCustomId("cancel_delete")
            .setLabel("إلغاء")
            .setStyle(Discord.ButtonStyle.Secondary)
        );

      const confirmMsg = await message.reply({ embeds: [confirmEmbed], components: [row] });

      // إنشاء مجمع للأزرار
      const collector = confirmMsg.createMessageComponentCollector({
        filter: i => i.user.id === message.author.id,
        time: 30000
      });

      collector.on("collect", async (interaction) => {
        if (interaction.customId === "confirm_delete_inactive") {
          await interaction.deferUpdate();

          // تحديث الرسالة لإظهار أن العملية قيد التنفيذ
          const processingEmbed = new Discord.EmbedBuilder()
            .setColor("Blue")
            .setTitle("🔄 جاري حذف الاشتراكات غير النشطة...")
            .setDescription("يرجى الانتظار قليلاً حتى تكتمل العملية.");

          await confirmMsg.edit({ embeds: [processingEmbed], components: [] });

          try {
            // قراءة ملف الاشتراكات مباشرة
            const fs = require('fs');
            const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

            if (!subsData || !subsData[user.id]) {
              return confirmMsg.edit({
                embeds: [
                  new Discord.EmbedBuilder()
                    .setColor("Red")
                    .setTitle("❌ خطأ")
                    .setDescription("حدث خطأ في قراءة ملف الاشتراكات")
                ]
              });
            }

            let totalTokensRecovered = 0;
            let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
            let currentTokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

            // استرجاع التوكنات من الاشتراكات غير النشطة
            for (const sub of inactiveSubscriptions) {
              if (sub.bots && Array.isArray(sub.bots)) {
                // إيقاف البوتات إذا كانت لا تزال تعمل
                if (sub.endDate > Date.now()) {
                  endSub(user.id, sub);
                }

                // استرجاع التوكنات
                let tokens = sub.bots;
                for (let i = 0; i < tokens.length; i++) {
                  let obj = tokens[i];
                  delete obj["type"];
                  currentTokens.push(obj);
                  totalTokensRecovered++;
                }
              }
            }

            // حذف الاشتراكات غير النشطة من مصفوفة المستخدم
            const activeSubscriptions = data.filter(sub =>
              sub.endDate > Date.now() && sub.ended !== true
            );

            // تحديث قاعدة البيانات في الذاكرة
            await db.set(user.id, activeSubscriptions);

            // تحديث ملف الاشتراكات
            subsData[user.id] = activeSubscriptions;
            fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));

            // تحديث ملف التوكنات
            tokensData.tokens = currentTokens;
            fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

            // تحديث قاعدة البيانات في الذاكرة
            await tokens_db.set("tokens", currentTokens);

            // إنشاء رسالة النجاح
            const successEmbed = new Discord.EmbedBuilder()
              .setColor("Green")
              .setTitle("✅ تم حذف الاشتراكات غير النشطة")
              .setDescription(`تم حذف ${inactiveSubscriptions.length} اشتراك غير نشط للمستخدم <@${user.id}> واسترجاع ${totalTokensRecovered} توكن إلى المخزون.`)
              .setTimestamp();

            await confirmMsg.edit({ embeds: [successEmbed] });

          } catch (error) {
            console.error("Error deleting inactive subscriptions:", error);

            const errorEmbed = new Discord.EmbedBuilder()
              .setColor("Red")
              .setTitle("❌ خطأ")
              .setDescription("حدث خطأ أثناء حذف الاشتراكات غير النشطة.")
              .setFooter({ text: error.message });

            await confirmMsg.edit({ embeds: [errorEmbed], components: [] });
          }
        } else if (interaction.customId === "cancel_delete") {
          await interaction.deferUpdate();

          const cancelEmbed = new Discord.EmbedBuilder()
            .setColor("Grey")
            .setTitle("تم إلغاء العملية")
            .setDescription("تم إلغاء عملية حذف الاشتراكات غير النشطة.");

          await confirmMsg.edit({ embeds: [cancelEmbed], components: [] });
        }
      });

      collector.on("end", (collected, reason) => {
        if (reason === "time" && collected.size === 0) {
          const timeoutEmbed = new Discord.EmbedBuilder()
            .setColor("Grey")
            .setTitle("انتهت المهلة")
            .setDescription("انتهت مهلة التأكيد. تم إلغاء عملية الحذف.");

          confirmMsg.edit({ embeds: [timeoutEmbed], components: [] }).catch(() => { });
        }
      });

      return;
    }

    // حذف اشتراك محدد
    let sub_id = args[2];
    if (!sub_id) return message.reply({ content: `❌ Please provide subscription id or use 'inactive' to delete all inactive subscriptions` });

    let data = await db.get(user.id);
    let subscription = data?.find(s => s.id == sub_id);
    let sub_index = data?.findIndex(s => s.id == sub_id);

    if (!subscription || sub_index == -1) return message.reply({ content: `❌ I can't find this subscription` });

    // إنشاء رسالة تأكيد
    const confirmEmbed = new Discord.EmbedBuilder()
      .setColor("Orange")
      .setTitle("⚠️ تأكيد حذف الاشتراك")
      .setDescription(`هل أنت متأكد من أنك تريد حذف الاشتراك \`${sub_id}\` للمستخدم <@${user.id}>؟`)
      .setFooter({ text: "سيتم إلغاء العملية بعد 30 ثانية" });

    const row = new Discord.ActionRowBuilder()
      .addComponents(
        new Discord.ButtonBuilder()
          .setCustomId("confirm_delete_single")
          .setLabel("تأكيد")
          .setStyle(Discord.ButtonStyle.Danger),
        new Discord.ButtonBuilder()
          .setCustomId("cancel_delete")
          .setLabel("إلغاء")
          .setStyle(Discord.ButtonStyle.Secondary)
      );

    const confirmMsg = await message.reply({ embeds: [confirmEmbed], components: [row] });

    // إنشاء مجمع للأزرار
    const collector = confirmMsg.createMessageComponentCollector({
      filter: i => i.user.id === message.author.id,
      time: 30000
    });

    collector.on("collect", async (interaction) => {
      if (interaction.customId === "confirm_delete_single") {
        await interaction.deferUpdate();

        // تحديث الرسالة لإظهار أن العملية قيد التنفيذ
        const processingEmbed = new Discord.EmbedBuilder()
          .setColor("Blue")
          .setTitle("🔄 جاري حذف الاشتراك...")
          .setDescription("يرجى الانتظار قليلاً حتى تكتمل العملية.");

        await confirmMsg.edit({ embeds: [processingEmbed], components: [] });

        try {
          if (subscription.endDate > Date.now()) {
            endSub(user.id, subscription);
          }

          let tokens = subscription.bots;
          for (let i = 0; i < tokens.length; i++) {
            let obj = tokens[i];
            delete obj["type"];
            tokens[i] = obj;
          }

          data.splice(sub_index, 1);

          // تحديث قاعدة البيانات في الذاكرة
          await db.set(user.id, data);

          // تحديث ملف الاشتراكات مباشرة
          try {
            const fs = require('fs');
            const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

            // حذف الاشتراك للمستخدم
            if (subsData[user.id]) {
              const subIndex = subsData[user.id].findIndex(s => s.id === sub_id);
              if (subIndex !== -1) {
                subsData[user.id].splice(subIndex, 1);

                // حفظ الملف
                fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
                console.log(`Subscription ${sub_id} deleted from file for user ${user.id}`);
              } else {
                console.error(`Subscription ${sub_id} not found in file for user ${user.id}`);
              }
            } else {
              console.error(`User ${user.id} not found in subscriptions file`);
            }
          } catch (error) {
            console.error(`Error updating subscriptions file:`, error);
          }

          // قراءة التوكنات مباشرة من الملف
          const fs = require('fs');
          let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
          let currentTokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

          console.log(`Current tokens in stock before adding: ${currentTokens.length}`);

          // إضافة التوكنات المستردة إلى المخزون
          for (let t of tokens) {
            currentTokens.push(t);
          }

          console.log(`Tokens after adding: ${currentTokens.length}`);

          // تحديث الملف مباشرة
          tokensData.tokens = currentTokens;
          fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

          // تحديث قاعدة البيانات في الذاكرة
          await tokens_db.set("tokens", currentTokens);

          // إنشاء رسالة النجاح
          const successEmbed = new Discord.EmbedBuilder()
            .setColor("Green")
            .setTitle("✅ تم حذف الاشتراك")
            .setDescription(`تم حذف الاشتراك \`${sub_id}\` للمستخدم <@${user.id}> واسترجاع ${tokens.length} توكن إلى المخزون.`)
            .setTimestamp();

          await confirmMsg.edit({ embeds: [successEmbed] });

        } catch (error) {
          console.error("Error deleting subscription:", error);

          const errorEmbed = new Discord.EmbedBuilder()
            .setColor("Red")
            .setTitle("❌ خطأ")
            .setDescription("حدث خطأ أثناء حذف الاشتراك.")
            .setFooter({ text: error.message });

          await confirmMsg.edit({ embeds: [errorEmbed], components: [] });
        }
      } else if (interaction.customId === "cancel_delete") {
        await interaction.deferUpdate();

        const cancelEmbed = new Discord.EmbedBuilder()
          .setColor("Grey")
          .setTitle("تم إلغاء العملية")
          .setDescription("تم إلغاء عملية حذف الاشتراك.");

        await confirmMsg.edit({ embeds: [cancelEmbed], components: [] });
      }
    });

    collector.on("end", (collected, reason) => {
      if (reason === "time" && collected.size === 0) {
        const timeoutEmbed = new Discord.EmbedBuilder()
          .setColor("Grey")
          .setTitle("انتهت المهلة")
          .setDescription("انتهت مهلة التأكيد. تم إلغاء عملية الحذف.");

        confirmMsg.edit({ embeds: [timeoutEmbed], components: [] }).catch(() => { });
      }
    });
  } else if (args[0] === prefix + "subs") {
    if (!config.devs.find(u => u === message.author.id)) return;
    if (args[1] === "all") {
      try {
        // قراءة ملف الاشتراكات مباشرة
        const fs = require('fs');
        const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

        if (!subsData || typeof subsData !== 'object') {
          return message.reply({ content: `❌ خطأ في قراءة ملف الاشتراكات` });
        }

        let subscriptions = [];

        // استخراج الاشتراكات من الملف
        for (const [userID, userData] of Object.entries(subsData)) {
          if (Array.isArray(userData) && userData.length > 0) {
            for (let sub of userData) {
              // إضافة معرف المستخدم إلى الاشتراك
              sub = { ...sub, userID };
              subscriptions.push(sub);
            }
          }
        }

        if (subscriptions.length < 1) {
          return message.reply({ content: `❌ لا يوجد اي اشتراكات حالياً` });
        }

        console.log(`Found ${subscriptions.length} subscriptions in file`);

        // تحويل الاشتراكات إلى نص للعرض
        const formattedSubscriptions = subscriptions.map((s, i) => {
          // استخراج معلومات البوتات
          let botsInfo = "unknown";
          if (s.bots && Array.isArray(s.bots) && s.bots.length > 0) {
            const musicBots = s.bots.filter(bot => bot.type && bot.type.toLowerCase() === "music");
            if (musicBots.length > 0 || s.type === "music") {
              botsInfo = `music x${s.bots.length}`;
            } else {
              const botTypes = s.bots
                .map(bot => bot.type || "unknown")
                .filter(type => type !== "unknown");

              if (botTypes.length > 0) {
                botsInfo = botTypes.map(type => `${type} x1`).join(", ");
              }
            }
          }

          // استخراج الوقت المتبقي
          let timeLeft = "ended";
          let isActive = false;

          if (s.endDate && typeof s.endDate === 'number') {
            // تعديل شرط الاشتراكات النشطة: التحقق من تاريخ الانتهاء وحقل ended
            if (s.endDate > Date.now() && s.ended !== true) {
              try {
                timeLeft = Ms(s.endDate - Date.now());
                isActive = true;
                console.log(`Active subscription found: ${s.id} for user ${s.userID}, endDate: ${new Date(s.endDate)}, ended: ${s.ended}`);
              } catch (error) {
                console.error(`Error calculating time left for subscription ${s.id}:`, error);
              }
            } else {
              // تحويل التاريخ إلى نص مقروء
              try {
                const endDate = new Date(s.endDate);
                timeLeft = `ended (${endDate.toLocaleDateString()})`;
                if (s.ended === true) {
                  console.log(`Subscription ${s.id} is marked as ended`);
                } else if (s.endDate <= Date.now()) {
                  console.log(`Subscription ${s.id} is expired (endDate: ${endDate})`);
                }
              } catch (error) {
                console.error(`Error formatting end date for subscription ${s.id}:`, error);
              }
            }
          }

          // استخراج معرف المستخدم
          const userID = s.userID || "unknown";

          // استخراج معرف الاشتراك
          const subID = s.id || "unknown";

          // استخراج معرف السيرفر
          const guildID = s.guildID || "unknown";

          return {
            text: `${i + 1} - <@${userID}> | **${botsInfo}** (SubID: \`${subID}\`) | **${timeLeft}** | Server: \`${guildID}\``,
            isActive: isActive
          };
        });

        // تقسيم الاشتراكات إلى صفحات
        const itemsPerPage = 15;
        const totalPages = Math.ceil(formattedSubscriptions.length / itemsPerPage);
        let currentPage = 0;

        // إنشاء الصفحة
        const generateEmbed = (page) => {
          const startIndex = page * itemsPerPage;
          const endIndex = Math.min(startIndex + itemsPerPage, formattedSubscriptions.length);
          const currentItems = formattedSubscriptions.slice(startIndex, endIndex);

          const embed = new EmbedBuilder()
            .setColor('#0099ff')
            .setTitle(`جميع الاشتراكات - الصفحة ${page + 1}/${totalPages}`)
            .setDescription(currentItems.map(item => item.text).join("\n"))
            .setFooter({ text: `إجمالي الاشتراكات: ${formattedSubscriptions.length}` })
            .setTimestamp();

          // إضافة حقول إضافية للمعلومات
          const activeCount = formattedSubscriptions.filter(s => s.isActive).length;
          embed.addFields([
            {
              name: 'الاشتراكات النشطة',
              value: `${activeCount}`,
              inline: true
            },
            {
              name: 'الاشتراكات المنتهية',
              value: `${formattedSubscriptions.length - activeCount}`,
              inline: true
            }
          ]);

          return embed;
        };

        // إنشاء أزرار التنقل
        const row = new Discord.ActionRowBuilder()
          .addComponents(
            new Discord.ButtonBuilder()
              .setCustomId('first')
              .setLabel('الأولى')
              .setStyle(Discord.ButtonStyle.Primary)
              .setDisabled(true),
            new Discord.ButtonBuilder()
              .setCustomId('previous')
              .setLabel('السابق')
              .setStyle(Discord.ButtonStyle.Primary)
              .setDisabled(true),
            new Discord.ButtonBuilder()
              .setCustomId('next')
              .setLabel('التالي')
              .setStyle(Discord.ButtonStyle.Primary)
              .setDisabled(totalPages <= 1),
            new Discord.ButtonBuilder()
              .setCustomId('last')
              .setLabel('الأخيرة')
              .setStyle(Discord.ButtonStyle.Primary)
              .setDisabled(totalPages <= 1)
          );

        // إرسال الرسالة الأولى مع الأزرار
        const embedMessage = await message.channel.send({
          embeds: [generateEmbed(currentPage)],
          components: [row]
        });

        // إنشاء مجمع التفاعلات
        const collector = embedMessage.createMessageComponentCollector({
          filter: (interaction) => {
            if (interaction.user.id !== message.author.id) {
              interaction.reply({ content: 'هذه الأزرار ليست لك!', ephemeral: true }).catch(err => console.error("Error replying to interaction:", err));
              return false;
            }
            return ['first', 'previous', 'next', 'last'].includes(interaction.customId);
          },
          time: 300000 // 5 دقائق
        });

        // معالجة التفاعلات
        collector.on('collect', async (interaction) => {
          try {
            // تحديث الصفحة الحالية بناءً على الزر المضغوط
            if (interaction.customId === 'first') {
              currentPage = 0;
            } else if (interaction.customId === 'previous') {
              currentPage = Math.max(0, currentPage - 1);
            } else if (interaction.customId === 'next') {
              currentPage = Math.min(totalPages - 1, currentPage + 1);
            } else if (interaction.customId === 'last') {
              currentPage = totalPages - 1;
            }

            // تحديث حالة الأزرار
            row.components[0].setDisabled(currentPage === 0); // زر الصفحة الأولى
            row.components[1].setDisabled(currentPage === 0); // زر السابق
            row.components[2].setDisabled(currentPage === totalPages - 1); // زر التالي
            row.components[3].setDisabled(currentPage === totalPages - 1); // زر الصفحة الأخيرة

            // تحديث الرسالة
            await interaction.update({
              embeds: [generateEmbed(currentPage)],
              components: [row]
            }).catch(err => {
              console.error("Error updating interaction:", err);
              // محاولة تحديث الرسالة الأصلية إذا فشل تحديث التفاعل
              embedMessage.edit({
                embeds: [generateEmbed(currentPage)],
                components: [row]
              }).catch(() => { });
            });
          } catch (error) {
            console.error("Error in collector:", error);
          }
        });

        // عند انتهاء وقت المجمع
        collector.on('end', () => {
          // تعطيل جميع الأزرار
          row.components.forEach(button => button.setDisabled(true));

          // تحديث الرسالة لإظهار أن الوقت قد انتهى
          embedMessage.edit({
            embeds: [generateEmbed(currentPage)],
            components: [row]
          }).catch(() => { });
        });
      } catch (error) {
        console.error("Error in subs all command:", error);
        message.reply({ content: `❌ حدث خطأ أثناء جلب الاشتراكات. الرجاء المحاولة مرة أخرى.` });
      }
    } else {
      let user = args[1] ? message.mentions.users.first() || await client.users.fetch(args[1]).catch(() => 0) : null;
      if (!user) return message.reply({ content: `❌ Please provide valid user` });
      let data = await db.get(user.id);
      if (!data || data.length < 1) return message.reply({ content: `❌ This user doesn't have subscriptions` });
      let subscriptions = data.map(e => ({ ...e, ended: e.endDate < Date.now(), createdAt: e.startDate, duration: e.endDate - e.startDate }));
      let n = 3;
      for (let i = 0; i < subscriptions.length; i += n) {
        let embeds = subscriptions.slice(i, i + n);
        embeds = embeds.map(e => {
          let em = new Discord.EmbedBuilder()
            .setColor(e.ended ? "Red" : "Green")
            .addFields([
              {
                name: `Subscription  ID:`,
                value: `${e.id}`,
                inline: true
              },
              {
                name: `Ended:`,
                value: `${e.ended}`,
                inline: true
              },
              {
                name: `Created At:`,
                value: `<t:${Math.floor(e.startDate / 1000)}:R>`,
                inline: true
              },
              {
                name: `Duration:`,
                value: `${ms(e.duration)}`,
                inline: true
              },
              {
                name: `Ends At:`,
                value: `<t:${Math.floor(e.endDate / 1000)}:R>`,
                inline: true
              },
              {
                name: `Bots Count:`,
                value: `${e.bots.length}`
              },
              {
                name: `Subscription type:`,
                value: `${e.bots.find(ee => ee.type?.toLowerCase() == "music") ? "music" : e.bots.map(t => t.type).join(", ")}`
              },
              {
                name: "Guild ID:",
                value: `${e.guildID}`
              }
            ])
          return em;
        });
        message.channel.send({ embeds: embeds });
      }
    }
  } else if (args[0] === prefix + "links") {
    if (!config.devs.find(u => u === message.author.id)) return;
    let user = args[1] ? message.mentions.users.first() || await client.users.fetch(args[1]).catch(() => 0) : null;
    if (!user) return message.reply({ content: `❌ Please provide valid user` });
    let sub_id = args[2];
    if (!sub_id) return message.reply({ content: `❌ Please provide subscription id` });
    let data = await db.get(user.id);
    let subscription = data?.find(s => s.id == sub_id);
    if (!subscription) return message.reply({ content: `❌ I can't find this subscription` });
    let links = subscription.bots.map(b => `https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${subscription.guildID}&disable_guild_select=true`);
    let n = 10;
    for (let i = 0; i < links.length; i += n) {
      let ee = links.slice(i, i + n);
      message.channel.send({ content: `${ee.join("\n")}` });
    }
  } else if (args[0] === prefix + "vip") {
    // أمر عرض الاشتراكات بتصميم مبسط
    let data = await db.get(message.author.id);

    if (!data || data.length < 1) {
      return message.reply({ content: `ليس لديك أي اشتراك.` });
    }

    // تصفية الاشتراكات النشطة فقط
    let activeSubscriptions = data.filter(sub => sub.endDate > Date.now() && sub.ended !== true);

    if (activeSubscriptions.length === 0) {
      return message.reply({ content: `ليس لديك اشتراكات نشطة حالياً.` });
    }

    // إنشاء نص الاشتراكات
    let subscriptionsText = "";

    activeSubscriptions.forEach((sub) => {
      // تحديد نوع الاشتراك بدقة
      let subType = "System"; // القيمة الافتراضية

      if (sub.type === "music" || sub.bots.find(bot => bot.type?.toLowerCase() === "music")) {
        subType = "Music";
      } else if (sub.type === "ticket" || sub.bots.find(bot => bot.type?.toLowerCase() === "ticket")) {
        subType = "Ticket";
      } else if (sub.type === "system" || sub.bots.find(bot => bot.type?.toLowerCase() === "system")) {
        subType = "System";
      }

      const timeLeft = prettyMilliseconds(sub.endDate - Date.now());

      subscriptionsText += `${subType} x${sub.bots.length} (SubID : ${sub.id}) : ${timeLeft}\n`;
    });


    // إرسال الرسالة
    const embed = new Discord.EmbedBuilder()
      .setColor("#ffffff")
      .setDescription(`\`\`\`${subscriptionsText}\`\`\``)

    message.reply({ embeds: [embed] });
  } else if (args[0] === prefix + "mysub") {
    let data = await db.get(message.author.id);
    if (!data || data.length < 1) return message.reply({ content: `ليس لديك أي اشتراك.` });

    // تصفية الاشتراكات النشطة فقط
    let activeSubscriptions = data.filter(sub => sub.endDate > Date.now() && sub.ended !== true);

    if (activeSubscriptions.length === 0) {
      return message.reply({ content: `ليس لديك اشتراكات نشطة حالياً.` });
    }

    // دالة لإنشاء الرسالة الرئيسية
    function createMainMessage() {
      const selectMenu = new Discord.StringSelectMenuBuilder()
        .setCustomId('select_subscription')
        .setPlaceholder('اختر اشتراكاً للتحكم به')
        .addOptions(
          activeSubscriptions.map(sub => {
            // تحديد نوع الاشتراك
            const subType = sub.type === "music" || sub.bots.find(bot => bot.type?.toLowerCase() === "music")
              ? "music"
              : sub.bots.map(bot => bot.type).join(", ");

            // حساب الوقت المتبقي
            const timeLeft = prettyMilliseconds(sub.endDate - Date.now());

            return {
              label: `${subType} (${sub.bots.length} بوت)`,
              description: `ينتهي في: ${timeLeft} | معرف الاشتراك: ${sub.id}`,
              value: sub.id
            };
          })
        );

      const row = new Discord.ActionRowBuilder().addComponents(selectMenu);

      const embed = new Discord.EmbedBuilder()
        .setColor("Blue")
        .setTitle(`إدارة الاشتراكات`)
        .setDescription(`لديك **${activeSubscriptions.length}** اشتراك نشط. اختر اشتراكاً من القائمة للتحكم به.`)

        .setFooter({ text: "اختر من القائمة اجراء لإدارة الاشتراك" })
        .setTimestamp();

      return { embeds: [embed], components: [row] };
    }

    // إرسال الرسالة الرئيسية
    const msg = await message.reply(createMainMessage());

    // إنشاء مجمع للتفاعلات
    const collector = msg.createMessageComponentCollector({
      filter: i => i.user.id === message.author.id,
      time: 300000 // 5 دقائق
    });

    // معالجة التفاعلات
    collector.on('collect', async interaction => {
      try {
        if (interaction.isStringSelectMenu() && interaction.customId === 'select_subscription') {
          // الحصول على معرف الاشتراك المحدد
          const selectedSubId = interaction.values[0];

          // البحث عن الاشتراك في البيانات
          const selectedSub = activeSubscriptions.find(sub => sub.id === selectedSubId);

          if (!selectedSub) {
            await interaction.reply({ content: "لم يتم العثور على الاشتراك المحدد.", ephemeral: true }).catch(err => console.error("Error replying to interaction:", err));
            return;
          }

          // إنشاء قائمة بالإجراءات المتاحة مع زر العودة
          const actionsMenu = new Discord.StringSelectMenuBuilder()
            .setCustomId(`subscription_actions_${selectedSubId}`)
            .setPlaceholder('اختر إجراءً للتحكم في الاشتراك')
            .addOptions([
              {
                label: 'إعادة تشغيل الاشتراك',
                description: 'إعادة تشغيل جميع البوتات في هذا الاشتراك',
                value: 'restart'
              },
              {
                label: 'نقل البوتات إلى سيرفر',
                description: 'تغيير السيرفر الذي تعمل فيه البوتات',
                value: 'transfer_server'
              },
              {
                label: 'نقل ملكية الاشتراك',
                description: 'نقل ملكية الاشتراك إلى مستخدم آخر',
                value: 'transfer_ownership'
              },
              {
                label: 'إرسال روابط البوتات',
                description: 'الحصول على روابط دعوة البوتات',
                value: 'show_links'
              }
            ]);

          // إضافة زر العودة
          const backButton = new Discord.ButtonBuilder()
            .setCustomId('back_to_main')
            .setLabel('العودة للقائمة الرئيسية')
            .setStyle(Discord.ButtonStyle.Secondary);

          const actionsRow = new Discord.ActionRowBuilder().addComponents(actionsMenu);
          const backRow = new Discord.ActionRowBuilder().addComponents(backButton);

          // إنشاء رسالة مع معلومات الاشتراك المحدد
          const subEmbed = new Discord.EmbedBuilder()
            .setColor("Green")
            .setTitle(`إدارة الاشتراك: ${selectedSubId}`)
            .setDescription(`اختر أحد الإجراءات أدناه للتحكم في هذا الاشتراك.`)
            .addFields([
              {
                name: "معرف الاشتراك",
                value: `${selectedSub.id}`,
                inline: true
              },
              {
                name: "عدد البوتات",
                value: `${selectedSub.bots.length} بوت`,
                inline: true
              },
              {
                name: "معرف السيرفر",
                value: `${selectedSub.guildID}`,
                inline: true
              },
              {
                name: "الوقت المتبقي",
                value: `${prettyMilliseconds(selectedSub.endDate - Date.now())}`,
                inline: false
              }
            ])
            .setFooter({ text: "اختر إجراءً من القائمة أعلاه أو اضغط العودة" })
            .setTimestamp();

          // تحديث الرسالة مع معلومات الاشتراك وقائمة الإجراءات
          await interaction.update({ embeds: [subEmbed], components: [actionsRow, backRow] });
        }

        // معالجة زر العودة للقائمة الرئيسية
        if (interaction.isButton() && interaction.customId === 'back_to_main') {
          await interaction.update(createMainMessage());
          return;
        }

        // معالجة اختيار الإجراء
        if (interaction.isStringSelectMenu() && interaction.customId.startsWith('subscription_actions_')) {
          // استخراج معرف الاشتراك من معرف القائمة
          const subId = interaction.customId.replace('subscription_actions_', '');

          // الحصول على الإجراء المحدد
          const selectedAction = interaction.values[0];

          // البحث عن الاشتراك في البيانات
          const subscription = activeSubscriptions.find(sub => sub.id === subId);

          if (!subscription) {
            await interaction.reply({ content: "لم يتم العثور على الاشتراك المحدد.", ephemeral: true });
            return;
          }

          // معالجة الإجراءات المختلفة
          switch (selectedAction) {
            case 'restart':
              // إعادة تشغيل الاشتراك
              const confirmButton = new Discord.ButtonBuilder()
                .setCustomId(`confirm_restart_${subscription.id}`)
                .setLabel("تأكيد إعادة التشغيل")
                .setStyle(Discord.ButtonStyle.Success);

              const cancelButton = new Discord.ButtonBuilder()
                .setCustomId('back_to_main')
                .setLabel("إلغاء")
                .setStyle(Discord.ButtonStyle.Danger);

              const confirmRow = new Discord.ActionRowBuilder().addComponents(confirmButton, cancelButton);

              const restartEmbed = new Discord.EmbedBuilder()
                .setColor("Orange")
                .setTitle("تأكيد إعادة التشغيل")
                .setDescription(`هل تريد إعادة تشغيل جميع البوتات في هذا الاشتراك؟`)
                .addFields([
                  {
                    name: "تفاصيل الاشتراك",
                    value: `معرف الاشتراك: ${subscription.id}\nعدد البوتات: ${subscription.bots.length}\nالسيرفر: ${subscription.guildID}`,
                    inline: false
                  },
                  {
                    name: "تحذير",
                    value: "سيتم إعادة تشغيل جميع البوتات وقد يستغرق ذلك بضع ثوانٍ.",
                    inline: false
                  }
                ])
                .setFooter({ text: "اضغط تأكيد لإعادة التشغيل أو إلغاء للعودة" });

              await interaction.update({ embeds: [restartEmbed], components: [confirmRow] });
              break;

            case 'transfer_server':
              // نقل السيرفر
              const serverEmbed = new Discord.EmbedBuilder()
                .setColor("Blue")
                .setTitle("نقل البوتات إلى سيرفر جديد")
                .setDescription("اضغط على زر إضافة معرف السيرفر لإدخال السيرفر الجديد.")
                .addFields([
                  {
                    name: "الاشتراك الحالي",
                    value: `معرف الاشتراك: ${subscription.id}\nالسيرفر الحالي: ${subscription.guildID}\nعدد البوتات: ${subscription.bots.length}`,
                    inline: false
                  },
                  {
                    name: "تعليمات",
                    value: "اضغط على الزر أدناه لفتح نموذج إدخال معرف السيرفر الجديد.",
                    inline: false
                  }
                ])
                .setFooter({ text: "تأكد من أن معرف السيرفر صحيح ومكون من 18-19 رقم" });

              const addServerButton = new Discord.ButtonBuilder()
                .setCustomId(`add_server_id_${subscription.id}`)
                .setLabel('إضافة معرف السيرفر')
                .setStyle(Discord.ButtonStyle.Primary);

              const backButton2 = new Discord.ButtonBuilder()
                .setCustomId('back_to_main')
                .setLabel('إلغاء')
                .setStyle(Discord.ButtonStyle.Secondary);

              const serverRow = new Discord.ActionRowBuilder().addComponents(addServerButton, backButton2);

              await interaction.update({ embeds: [serverEmbed], components: [serverRow] });
              break;

            case 'show_links':
              // عرض روابط البوتات
              const linksEmbed = new Discord.EmbedBuilder()
                .setColor("Blue")
                .setTitle("🔗 روابط دعوة الاشتراك")
                .setDescription("تم إرسال جميع روابط البوتات إلى رسائلك الخاصة.")
                .addFields([
                  {
                    name: "تفاصيل الاشتراك",
                    value: `**معرف الاشتراك:** \`${subscription.id}\`\n**عدد البوتات:** ${subscription.bots.length}\n**السيرفر:** \`${subscription.guildID}\``,
                    inline: false
                  },
                  {
                    name: "ملاحظة",
                    value: "إذا لم تستلم الروابط، تأكد من فتح الرسائل الخاصة.",
                    inline: false
                  }
                ])
                .setFooter({ text: "💡 اضغط العودة للقائمة الرئيسية" });

              const backButton8 = new Discord.ButtonBuilder()
                .setCustomId('back_to_main')
                .setLabel('🔙 العودة للقائمة الرئيسية')
                .setStyle(Discord.ButtonStyle.Secondary);

              const backRow8 = new Discord.ActionRowBuilder().addComponents(backButton8);

              await interaction.update({ embeds: [linksEmbed], components: [backRow8] });

              // إرسال الروابط في الخاص
              let links = subscription.bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${subscription.guildID}&disable_guild_select=true`);
              let n = 10;
              for (let i = 0; i < links.length; i += n) {
                let ee = links.slice(i, i + n);
                await message.author.send({ content: `${ee.join("\n")}` }).catch(() => 0);
              }
              break;

            case 'transfer_ownership':
              // نقل الملكية
              const ownershipEmbed = new Discord.EmbedBuilder()
                .setColor("Orange")
                .setTitle("نقل ملكية الاشتراك")
                .setDescription("يرجى إدخال معرف المستخدم الجديد أو منشن له في الرسالة التالية.")
                .addFields([
                  {
                    name: "الاشتراك الحالي",
                    value: `**معرف الاشتراك:** \`${subscription.id}\`\n**المالك الحالي:** <@${message.author.id}>\n**عدد البوتات:** ${subscription.bots.length}\n**السيرفر:** \`${subscription.guildID}\``,
                    inline: false
                  },
                  {
                    name: "تعليمات",
                    value: "اكتب معرف المستخدم الجديد أو اعمل منشن له في الدردشة خلال 60 ثانية.\n\n**مثال:**\n`123456789012345678`\n`@username`",
                    inline: false
                  },
                  {
                    name: "تحذير مهم",
                    value: "بعد نقل الملكية، لن تتمكن من إدارة هذا الاشتراك مرة أخرى!\nتأكد من أن المستخدم الجديد موثوق.",
                    inline: false
                  }
                ])
                .setFooter({ text: "💡 تأكد من صحة معرف المستخدم قبل الإرسال" });

              const backButton9 = new Discord.ButtonBuilder()
                .setCustomId('back_to_main')
                .setLabel('🔙 إلغاء والعودة')
                .setStyle(Discord.ButtonStyle.Secondary);

              const backRow9 = new Discord.ActionRowBuilder().addComponents(backButton9);

              await interaction.update({ embeds: [ownershipEmbed], components: [backRow9] });

              // انتظار رد المستخدم
              const ownershipFilter = m => m.author.id === message.author.id;
              const ownershipCollector = message.channel.createMessageCollector({ filter: ownershipFilter, time: 60000, max: 1 });

              ownershipCollector.on('collect', async m => {
                let newOwnerId = m.content.trim();

                // التحقق من المنشن
                if (m.mentions.users.size > 0) {
                  newOwnerId = m.mentions.users.first().id;
                }

                // التحقق من صحة معرف المستخدم
                if (!newOwnerId || isNaN(newOwnerId) || newOwnerId.length < 15) {
                  const errorEmbed = new Discord.EmbedBuilder()
                    .setColor("Red")
                    .setTitle("❌ معرف مستخدم غير صالح")
                    .setDescription("يرجى التأكد من أن معرف المستخدم صحيح أو اعمل منشن للمستخدم.")
                    .addFields([
                      {
                        name: "📝 أمثلة صحيحة",
                        value: "`123456789012345678`\n`@username`",
                        inline: false
                      }
                    ])
                    .setFooter({ text: "💡 اضغط العودة لإعادة المحاولة" });

                  const backButton10 = new Discord.ButtonBuilder()
                    .setCustomId('back_to_main')
                    .setLabel('🔙 العودة')
                    .setStyle(Discord.ButtonStyle.Secondary);

                  const backRow10 = new Discord.ActionRowBuilder().addComponents(backButton10);

                  await msg.edit({ embeds: [errorEmbed], components: [backRow10] });
                  await m.delete().catch(() => {});
                  return;
                }

                // التحقق من أن المستخدم ليس نفس المالك الحالي
                if (newOwnerId === message.author.id) {
                  const sameUserEmbed = new Discord.EmbedBuilder()
                    .setColor("Orange")
                    .setTitle("⚠️ نفس المستخدم")
                    .setDescription("لا يمكنك نقل الملكية لنفسك! أنت المالك الحالي بالفعل.")
                    .setFooter({ text: "💡 اضغط العودة لاختيار مستخدم مختلف" });

                  const backButton11 = new Discord.ButtonBuilder()
                    .setCustomId('back_to_main')
                    .setLabel('🔙 العودة')
                    .setStyle(Discord.ButtonStyle.Secondary);

                  const backRow11 = new Discord.ActionRowBuilder().addComponents(backButton11);

                  await msg.edit({ embeds: [sameUserEmbed], components: [backRow11] });
                  await m.delete().catch(() => {});
                  return;
                }

                // محاولة جلب بيانات المستخدم للتأكد من وجوده
                let newOwner;
                try {
                  newOwner = await client.users.fetch(newOwnerId);
                } catch (error) {
                  const notFoundEmbed = new Discord.EmbedBuilder()
                    .setColor("Red")
                    .setTitle("❌ مستخدم غير موجود")
                    .setDescription("لم يتم العثور على مستخدم بهذا المعرف. تأكد من صحة المعرف.")
                    .setFooter({ text: "💡 اضغط العودة لإعادة المحاولة" });

                  const backButton12 = new Discord.ButtonBuilder()
                    .setCustomId('back_to_main')
                    .setLabel('🔙 العودة')
                    .setStyle(Discord.ButtonStyle.Secondary);

                  const backRow12 = new Discord.ActionRowBuilder().addComponents(backButton12);

                  await msg.edit({ embeds: [notFoundEmbed], components: [backRow12] });
                  await m.delete().catch(() => {});
                  return;
                }

                // التحقق من أن المستخدم ليس بوت
                if (newOwner.bot) {
                  const botUserEmbed = new Discord.EmbedBuilder()
                    .setColor("Red")
                    .setTitle("❌ لا يمكن نقل الملكية لبوت")
                    .setDescription("لا يمكن نقل ملكية الاشتراك إلى بوت. يجب أن يكون المستخدم الجديد مستخدم حقيقي.")
                    .setFooter({ text: "💡 اضغط العودة لاختيار مستخدم آخر" });

                  const backButton13 = new Discord.ButtonBuilder()
                    .setCustomId('back_to_main')
                    .setLabel('🔙 العودة')
                    .setStyle(Discord.ButtonStyle.Secondary);

                  const backRow13 = new Discord.ActionRowBuilder().addComponents(backButton13);

                  await msg.edit({ embeds: [botUserEmbed], components: [backRow13] });
                  await m.delete().catch(() => {});
                  return;
                }

                // عرض تأكيد نقل الملكية
                const confirmEmbed = new Discord.EmbedBuilder()
                  .setColor("Red")
                  .setTitle("⚠️ تأكيد نقل الملكية")
                  .setDescription("هل أنت متأكد من أنك تريد نقل ملكية هذا الاشتراك؟")
                  .addFields([
                    {
                      name: "📊 تفاصيل النقل",
                      value: `**معرف الاشتراك:** \`${subscription.id}\`\n**المالك الحالي:** <@${message.author.id}>\n**المالك الجديد:** <@${newOwnerId}> (${newOwner.username})\n**عدد البوتات:** ${subscription.bots.length}\n**السيرفر:** \`${subscription.guildID}\``,
                      inline: false
                    },
                    {
                      name: "🚨 تحذير نهائي",
                      value: "**هذا الإجراء لا يمكن التراجع عنه!**\nبعد النقل، لن تتمكن من إدارة هذا الاشتراك مرة أخرى.\nالمالك الجديد سيحصل على كامل الصلاحيات.",
                      inline: false
                    }
                  ])
                  .setFooter({ text: "💡 اضغط تأكيد النقل فقط إذا كنت متأكد تماماً" });

                const confirmTransferButton = new Discord.ButtonBuilder()
                  .setCustomId(`confirm_transfer_${subscription.id}_${newOwnerId}`)
                  .setLabel("✅ تأكيد نقل الملكية")
                  .setStyle(Discord.ButtonStyle.Danger);

                const cancelTransferButton = new Discord.ButtonBuilder()
                  .setCustomId('back_to_main')
                  .setLabel("❌ إلغاء")
                  .setStyle(Discord.ButtonStyle.Secondary);

                const confirmRow = new Discord.ActionRowBuilder().addComponents(confirmTransferButton, cancelTransferButton);

                await msg.edit({ embeds: [confirmEmbed], components: [confirmRow] });
                await m.delete().catch(() => {});
              });

              ownershipCollector.on('end', collected => {
                if (collected.size === 0) {
                  const timeoutEmbed = new Discord.EmbedBuilder()
                    .setColor("Orange")
                    .setTitle("⏰ انتهت المهلة الزمنية")
                    .setDescription("لم يتم إدخال معرف المستخدم في الوقت المحدد.")
                    .setFooter({ text: "💡 اضغط العودة للمحاولة مرة أخرى" });

                  const backButton14 = new Discord.ButtonBuilder()
                    .setCustomId('back_to_main')
                    .setLabel('🔙 العودة')
                    .setStyle(Discord.ButtonStyle.Secondary);

                  const backRow14 = new Discord.ActionRowBuilder().addComponents(backButton14);

                  msg.edit({ embeds: [timeoutEmbed], components: [backRow14] }).catch(() => {});
                }
              });
              break;
          }
        }

        // معالجة تأكيد إعادة التشغيل
        if (interaction.isButton() && interaction.customId.startsWith('confirm_restart_')) {
          const subId = interaction.customId.replace('confirm_restart_', '');
          const subscription = activeSubscriptions.find(sub => sub.id === subId);

          if (!subscription) {
            await interaction.reply({ content: "❌ لم يتم العثور على الاشتراك المحدد.", ephemeral: true });
            return;
          }

          // إعادة تشغيل الاشتراك
          try {
            restartSub(subscription);

            const successEmbed = new Discord.EmbedBuilder()
              .setColor("Green")
              .setTitle("✅ تم إعادة التشغيل بنجاح")
              .setDescription("تم إعادة تشغيل جميع البوتات في الاشتراك بنجاح.")
              .addFields([
                {
                  name: "📊 تفاصيل الاشتراك",
                  value: `**معرف الاشتراك:** \`${subscription.id}\`\n**عدد البوتات:** ${subscription.bots.length}\n**السيرفر:** \`${subscription.guildID}\``,
                  inline: false
                },
                {
                  name: "⏰ وقت إعادة التشغيل",
                  value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
                  inline: false
                }
              ])
              .setFooter({ text: "💡 قد يستغرق ظهور البوتات بضع ثوانٍ" });

            const backButton10 = new Discord.ButtonBuilder()
              .setCustomId('back_to_main')
              .setLabel('🔙 العودة للقائمة الرئيسية')
              .setStyle(Discord.ButtonStyle.Secondary);

            const backRow10 = new Discord.ActionRowBuilder().addComponents(backButton10);

            await interaction.update({ embeds: [successEmbed], components: [backRow10] });

          } catch (error) {
            console.error("Error restarting subscription:", error);

            const errorEmbed = new Discord.EmbedBuilder()
              .setColor("Red")
              .setTitle("❌ خطأ في إعادة التشغيل")
              .setDescription("حدث خطأ أثناء محاولة إعادة تشغيل الاشتراك.")
              .setFooter({ text: "💡 اضغط العودة للمحاولة مرة أخرى" });

            const backButton11 = new Discord.ButtonBuilder()
              .setCustomId('back_to_main')
              .setLabel('🔙 العودة')
              .setStyle(Discord.ButtonStyle.Secondary);

            const backRow11 = new Discord.ActionRowBuilder().addComponents(backButton11);

            await interaction.update({ embeds: [errorEmbed], components: [backRow11] });
          }
        }

        // معالجة زر إضافة معرف السيرفر
        if (interaction.isButton() && interaction.customId.startsWith('add_server_id_')) {
          const subId = interaction.customId.replace('add_server_id_', '');
          const subscription = activeSubscriptions.find(sub => sub.id === subId);

          if (!subscription) {
            await interaction.reply({ content: "لم يتم العثور على الاشتراك المحدد.", ephemeral: true });
            return;
          }

          // إنشاء Modal لإدخال معرف السيرفر
          const modal = new Discord.ModalBuilder()
            .setCustomId(`server_transfer_${subId}`)
            .setTitle('نقل السيرفر');

          // حقل معرف السيرفر الجديد
          const serverIdInput = new Discord.TextInputBuilder()
            .setCustomId('new_server_id')
            .setLabel('معرف السيرفر الجديد')
            .setPlaceholder('1234567890123456789')
            .setStyle(Discord.TextInputStyle.Short)
            .setMinLength(15)
            .setMaxLength(20)
            .setRequired(true);

          const serverRow = new Discord.ActionRowBuilder().addComponents(serverIdInput);
          modal.addComponents(serverRow);

          await interaction.showModal(modal);
        }

        // معالجة تأكيد نقل الملكية
        if (interaction.isButton() && interaction.customId.startsWith('confirm_transfer_')) {
          const parts = interaction.customId.replace('confirm_transfer_', '').split('_');
          const subId = parts[0];
          const newOwnerId = parts[1];

          const subscription = activeSubscriptions.find(sub => sub.id === subId);

          if (!subscription) {
            await interaction.reply({ content: "❌ لم يتم العثور على الاشتراك المحدد.", ephemeral: true });
            return;
          }

          // تنفيذ نقل الملكية
          try {
            // الحصول على بيانات المستخدم الجديد
            const newOwner = await client.users.fetch(newOwnerId);

            // تحديث قاعدة البيانات الرئيسية
            let currentOwnerData = await db.get(message.author.id);
            let newOwnerData = await db.get(newOwnerId) || [];

            // العثور على الاشتراك في بيانات المالك الحالي
            const subIndex = currentOwnerData.findIndex(s => s.id === subId);

            if (subIndex === -1) {
              throw new Error("Subscription not found in current owner data");
            }

            // نسخ الاشتراك
            const transferredSub = { ...currentOwnerData[subIndex] };

            // إزالة الاشتراك من المالك الحالي
            currentOwnerData.splice(subIndex, 1);

            // إضافة الاشتراك للمالك الجديد
            newOwnerData.push(transferredSub);

            // تحديث قاعدة البيانات
            await db.set(message.author.id, currentOwnerData);
            await db.set(newOwnerId, newOwnerData);

            // تحديث ملف الاشتراكات
            const fs = require('fs');
            const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

            // إزالة من المالك الحالي
            if (subsData[message.author.id]) {
              const fileSubIndex = subsData[message.author.id].findIndex(s => s.id === subId);
              if (fileSubIndex !== -1) {
                subsData[message.author.id].splice(fileSubIndex, 1);

                // إزالة المستخدم من الملف إذا لم تعد لديه اشتراكات
                if (subsData[message.author.id].length === 0) {
                  delete subsData[message.author.id];
                }
              }
            }

            // إضافة للمالك الجديد
            if (!subsData[newOwnerId]) {
              subsData[newOwnerId] = [];
            }
            subsData[newOwnerId].push(transferredSub);

            // حفظ الملف
            fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));

            // إرسال إشعار للمالك الجديد
            try {
              const notificationEmbed = new Discord.EmbedBuilder()
                .setColor("Green")
                .setTitle("🎉 تم نقل ملكية اشتراك إليك!")
                .setDescription(`تم نقل ملكية اشتراك إليك من قبل <@${message.author.id}>`)
                .addFields([
                  {
                    name: "📊 تفاصيل الاشتراك",
                    value: `**معرف الاشتراك:** \`${subscription.id}\`\n**عدد البوتات:** ${subscription.bots.length}\n**السيرفر:** \`${subscription.guildID}\`\n**النوع:** ${subscription.type === "music" || subscription.bots.find(bot => bot.type?.toLowerCase() === "music") ? "🎵 موسيقى" : "🤖 عام"}`,
                    inline: false
                  },
                  {
                    name: "📝 ملاحظة",
                    value: `يمكنك الآن إدارة هذا الاشتراك باستخدام الأمر \`${prefix}mysub\``,
                    inline: false
                  }
                ])
                .setFooter({ text: "مبروك! أنت الآن المالك الجديد لهذا الاشتراك" })
                .setTimestamp();

              await newOwner.send({ embeds: [notificationEmbed] });
            } catch (dmError) {
              console.log("Could not send DM to new owner:", dmError);
            }

            // عرض رسالة النجاح
            const successEmbed = new Discord.EmbedBuilder()
              .setColor("Green")
              .setTitle("✅ تم نقل الملكية بنجاح")
              .setDescription("تم نقل ملكية الاشتراك بنجاح إلى المستخدم الجديد.")
              .addFields([
                {
                  name: "📊 تفاصيل النقل",
                  value: `**معرف الاشتراك:** \`${subscription.id}\`\n**المالك السابق:** <@${message.author.id}>\n**المالك الجديد:** <@${newOwnerId}> (${newOwner.username})\n**عدد البوتات:** ${subscription.bots.length}\n**السيرفر:** \`${subscription.guildID}\``,
                  inline: false
                },
                {
                  name: "📝 ملاحظة",
                  value: "تم إرسال إشعار للمالك الجديد. لم تعد تملك هذا الاشتراك.",
                  inline: false
                },
                {
                  name: "⏰ وقت النقل",
                  value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
                  inline: false
                }
              ])
              .setFooter({ text: "💡 شكراً لاستخدام خدماتنا" });

            const backButton15 = new Discord.ButtonBuilder()
              .setCustomId('back_to_main')
              .setLabel('🔙 العودة للقائمة الرئيسية')
              .setStyle(Discord.ButtonStyle.Secondary);

            const backRow15 = new Discord.ActionRowBuilder().addComponents(backButton15);

            await interaction.update({ embeds: [successEmbed], components: [backRow15] });

            // تسجيل العملية في السجلات
            console.log(`Ownership transfer completed: Subscription ${subId} transferred from ${message.author.id} to ${newOwnerId}`);

            // إرسال لوج للمسؤولين إذا كان هناك قناة لوج
            const logChannel = client.channels.cache.get(config.log_channel);
            if (logChannel) {
              const logEmbed = new Discord.EmbedBuilder()
                .setColor("Blue")
                .setTitle("📋 نقل ملكية اشتراك")
                .addFields([
                  {
                    name: "معرف الاشتراك",
                    value: subscription.id,
                    inline: true
                  },
                  {
                    name: "المالك السابق",
                    value: `<@${message.author.id}> (${message.author.id})`,
                    inline: true
                  },
                  {
                    name: "المالك الجديد",
                    value: `<@${newOwnerId}> (${newOwnerId})`,
                    inline: true
                  },
                  {
                    name: "عدد البوتات",
                    value: subscription.bots.length.toString(),
                    inline: true
                  },
                  {
                    name: "السيرفر",
                    value: subscription.guildID,
                    inline: true
                  },
                  {
                    name: "وقت النقل",
                    value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
                    inline: true
                  }
                ])
                .setTimestamp();

              await logChannel.send({ embeds: [logEmbed] }).catch(() => {});
            }

          } catch (error) {
            console.error("Error transferring ownership:", error);

            const errorEmbed = new Discord.EmbedBuilder()
              .setColor("Red")
              .setTitle("❌ خطأ في نقل الملكية")
              .setDescription("حدث خطأ أثناء محاولة نقل ملكية الاشتراك. يرجى المحاولة مرة أخرى.")
              .addFields([
                {
                  name: "تفاصيل الخطأ",
                  value: error.message || "خطأ غير معروف",
                  inline: false
                }
              ])
              .setFooter({ text: "💡 اضغط العودة للمحاولة مرة أخرى" });

            const backButton16 = new Discord.ButtonBuilder()
              .setCustomId('back_to_main')
              .setLabel('🔙 العودة')
              .setStyle(Discord.ButtonStyle.Secondary);

            const backRow16 = new Discord.ActionRowBuilder().addComponents(backButton16);

            await interaction.update({ embeds: [errorEmbed], components: [backRow16] });
          }
        }
      } catch (error) {
        console.error("Error in subscription collector:", error);
      }
    });

    // عند انتهاء وقت المجمع
    collector.on('end', () => {
      // إنشاء رسالة انتهاء الوقت
      const timeoutEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("انتهت جلسة الإدارة")
        .setDescription("انتهت مهلة إدارة الاشتراكات. استخدم الأمر مرة أخرى للمتابعة.")
        .setFooter({ text: `استخدم ${prefix}mysub لبدء جلسة جديدة` })
        .setTimestamp();

      msg.edit({ embeds: [timeoutEmbed], components: [] }).catch(() => { });
    });
  } else if (args[0] === prefix + "mylinks") {
    let sub_id = args[1];
    if (!sub_id) return message.reply({ content: `**- أرفق معرف الأشتراك الخاص بك**` });
    let data = await db.get(message.author.id);
    let subscription = data?.find(s => s.id == sub_id);
    if (!subscription) return message.reply({ content: `❌ I can't find this subscription` });
    if (!subscription.endDate || subscription.endDate <= Date.now()) return message.reply({ content: `❌ You subscription was ended` });

    // إرسال الروابط بالطريقة القديمة مع إضافة رقم لكل رابط
    let links = subscription.bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${subscription.guildID}&disable_guild_select=true`);
    let n = 10;
    for (let i = 0; i < links.length; i += n) {
      let ee = links.slice(i, i + n);
      message.author.send({ content: `${ee.join("\n")}` }).catch(() => 0);
    }
    message.reply({ content: `**- تم أرفاق جميع الروابط بالخاص, اذا لم تستلمه تأكد من فتحه**` });
  } else if (args[0] === prefix + "setserver") {
    let sub_id = args[1];
    if (!sub_id) return message.reply({ content: `❌ Please provide subscription id` });
    let data = await db.get(message.author.id);
    let subscription = data?.findIndex(s => s.id == sub_id);
    if (!data || subscription == -1) return message.reply({ content: `❌ I can't find this subscription` });
    if (!data[subscription].endDate || data[subscription].endDate <= Date.now()) return message.reply({ content: `❌ You subscription was ended` });
    let guild_id = args[2];
    if (!guild_id || isNaN(guild_id) || guild_id.length < 15) return message.channel.send({ content: `❌ Please provide valid guild id.` });
    if (guild_id === data[subscription].guildID) return message.channel.send({ content: `🧐 **That's subscription server already.**` });
    data[subscription].guildID = guild_id;
    let sub_data = data[subscription];
    await db.set(message.author.id, data);
    message.reply({ content: `✅ **Done set subscription's server to ${guild_id}.**`, embeds: [], components: [] }).catch(() => 0);
    // إرسال الروابط بالطريقة القديمة مع إضافة رقم لكل رابط
    let links = sub_data.bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${sub_data.guildID}&disable_guild_select=true`);
    let n = 10;
    for (let i = 0; i < links.length; i += n) {
      let ee = links.slice(i, i + n);
      await message.author.send({ content: `${ee.join("\n")}` }).catch(() => 0);
    }
    restartSub(sub_data);
  } else if (args[0] === prefix + "token") {
    if (!config.devs.find(u => u === message.author.id)) return;
    let token = args[1];
    let attachment = message.attachments.size > 0 ? message.attachments.first() : null;
    if (!token && !attachment) return message.reply({ content: `❌ Please provide token or file.` });
    // قراءة التوكنات مباشرة من الملف
    const fs = require('fs');
    let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
    let t_data = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

    console.log(`Current tokens in stock: ${t_data.length}`);

    if (attachment) {
      if (!attachment.contentType.startsWith("text")) return message.reply({ content: `❌ Attachment type must be .txt` });
      axios.get(attachment.url).then(async daata => {
        daata = daata.data.split("\n").filter(i => i != "" && !t_data.find(e => e.botToken == i));
        await message.reply({ content: `Adding **${daata.length}** token to stock` });
        let addedCount = 0;

        for (let tok of daata) {
          let id = await getId(tok);
          if (!id) {
            await message.channel.send({ content: `❌ \`${tok}\`` });
          } else {
            let tok_data = {
              botToken: tok,
              botId: id
            }
            t_data.push(tok_data);
            addedCount++;
          }
        }

        // تحديث الملف مباشرة
        tokensData.tokens = t_data;
        fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

        // تحديث قاعدة البيانات في الذاكرة
        await tokens_db.set("tokens", t_data);

        console.log(`Added ${addedCount} tokens, new total: ${t_data.length}`);
        message.reply({ content: `✅ Done add ${addedCount} tokens to stock. Total tokens now: ${t_data.length}` }).catch(() => 0);
      }).catch((error) => {
        console.error("Error getting tokens from file:", error);
        message.reply({ content: `❌ I can't get tokens from this file.`, ephemeral: true }).catch(() => 0);
      });
    } else if (token) {
      if (t_data.find(e => e.botToken == token)) return message.reply({ content: `❌ You already added this token before` });
      let id = await getId(token);
      if (!id) return message.reply({ content: `❌ Invalid token` });

      let tok_data = {
        botToken: token,
        botId: id
      }

      // إضافة التوكن إلى المصفوفة
      t_data.push(tok_data);

      // تحديث الملف مباشرة
      tokensData.tokens = t_data;
      fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

      // تحديث قاعدة البيانات في الذاكرة
      await tokens_db.set("tokens", t_data);

      console.log(`Added 1 token, new total: ${t_data.length}`);
      message.reply({ content: `✅ - **Done add this token to stock. Total tokens now: ${t_data.length}**` });
    }
  } else if (args[0] === prefix + "tokens") {
    if (!config.devs.find(u => u === message.author.id)) return;
    try {
      // قراءة الملف مباشرة للتأكد من الحصول على البيانات الصحيحة
      const fs = require('fs');
      const tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));

      // التحقق من وجود مفتاح tokens وأنه مصفوفة
      const tokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens)
        ? tokensData.tokens
        : [];

      message.reply({ content: `Total tokens in stock is **${tokens.length}**` });

      // تحديث قاعدة البيانات في الذاكرة لتتطابق مع الملف
      if (tokens.length > 0) {
        await tokens_db.set("tokens", tokens);
        console.log(`Updated tokens_db in memory with ${tokens.length} tokens from file`);
      }
    } catch (error) {
      console.error("Error in tokens command:", error);
      message.reply({ content: `❌ حدث خطأ أثناء جلب التوكنات. الرجاء المحاولة مرة أخرى.` });
    }
  } else if (args[0] === prefix + "stats") {
    // التحقق من صلاحيات المستخدم
    const isAdmin = config.devs.find(u => u === message.author.id);

    // إنشاء رسالة انتظار
    const loadingEmbed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle("⏳ جاري جمع إحصائيات النظام...")
      .setDescription("يرجى الانتظار قليلاً بينما نقوم بجمع المعلومات.")
      .setTimestamp();

    const loadingMsg = await message.reply({ embeds: [loadingEmbed] });

    try {
      // جمع إحصائيات الاشتراكات
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

      // إحصائيات عامة
      let totalSubscriptions = 0;
      let activeSubscriptions = 0;
      let expiredSubscriptions = 0;
      let totalBots = 0;
      let activeBots = 0;
      let musicBots = 0;
      let otherBots = 0;
      let uniqueUsers = 0;
      let uniqueServers = new Set();

      // تحليل بيانات الاشتراكات
      if (subsData && typeof subsData === 'object') {
        uniqueUsers = Object.keys(subsData).length;

        for (const [userId, userSubs] of Object.entries(subsData)) {
          if (Array.isArray(userSubs)) {
            totalSubscriptions += userSubs.length;

            for (const sub of userSubs) {
              // إضافة السيرفر إلى القائمة الفريدة
              if (sub.guildID) {
                uniqueServers.add(sub.guildID);
              }

              // التحقق من حالة الاشتراك
              const isActive = sub.endDate > Date.now() && sub.ended !== true;

              if (isActive) {
                activeSubscriptions++;

                // عدد البوتات النشطة
                if (sub.bots && Array.isArray(sub.bots)) {
                  activeBots += sub.bots.length;

                  // تصنيف البوتات
                  for (const bot of sub.bots) {
                    if (bot.type && bot.type.toLowerCase() === "music") {
                      musicBots++;
                    } else {
                      otherBots++;
                    }
                  }
                }
              } else {
                expiredSubscriptions++;
              }

              // إجمالي عدد البوتات
              if (sub.bots && Array.isArray(sub.bots)) {
                totalBots += sub.bots.length;
              }
            }
          }
        }
      }

      // جمع إحصائيات التوكنات
      const tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
      const availableTokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens)
        ? tokensData.tokens.length
        : 0;

      // جمع إحصائيات النظام باستخدام PM2
      let systemStats = {
        cpuUsage: "غير متاح",
        memoryUsage: "غير متاح",
        uptime: "غير متاح",
        totalProcesses: 0,
        onlineProcesses: 0
      };

      // الحصول على معلومات العمليات من PM2
      const pm2List = await new Promise((resolve) => {
        pm2.list((err, list) => {
          if (err) {
            console.error("Error getting PM2 list:", err);
            resolve([]);
          } else {
            resolve(list || []);
          }
        });
      });

      if (pm2List.length > 0) {
        systemStats.totalProcesses = pm2List.length;
        systemStats.onlineProcesses = pm2List.filter(p => p.pm2_env.status === "online").length;

        // حساب متوسط استخدام الموارد
        let totalCpu = 0;
        let totalMemory = 0;
        let processCount = 0;

        for (const process of pm2List) {
          if (process.monit) {
            totalCpu += process.monit.cpu || 0;
            totalMemory += process.monit.memory || 0;
            processCount++;
          }
        }

        if (processCount > 0) {
          systemStats.cpuUsage = `${(totalCpu / processCount).toFixed(2)}%`;
          systemStats.memoryUsage = `${(totalMemory / (1024 * 1024)).toFixed(2)} MB`;
        }

        // حساب وقت التشغيل
        const oldestProcess = pm2List.reduce((oldest, current) => {
          if (!oldest || (current.pm2_env && current.pm2_env.pm_uptime < oldest.pm2_env.pm_uptime)) {
            return current;
          }
          return oldest;
        }, null);

        if (oldestProcess && oldestProcess.pm2_env && oldestProcess.pm2_env.pm_uptime) {
          const uptime = Date.now() - oldestProcess.pm2_env.pm_uptime;
          systemStats.uptime = prettyMilliseconds(uptime);
        }
      }

      // إنشاء رسالة الإحصائيات
      const statsEmbed = new Discord.EmbedBuilder()
        .setColor("#00FF00")
        .setTitle("📊 إحصائيات نظام إدارة البوتات")
        .setDescription(`إحصائيات شاملة عن حالة النظام والاشتراكات والبوتات.`)
        .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
        .addFields([
          {
            name: "📈 إحصائيات الاشتراكات",
            value: [
              `**إجمالي الاشتراكات:** ${totalSubscriptions}`,
              `**الاشتراكات النشطة:** ${activeSubscriptions}`,
              `**الاشتراكات المنتهية:** ${expiredSubscriptions}`,
              `**عدد المستخدمين:** ${uniqueUsers}`,
              `**عدد السيرفرات:** ${uniqueServers.size}`
            ].join('\n'),
            inline: false
          },
          {
            name: "🤖 إحصائيات البوتات",
            value: [
              `**إجمالي البوتات:** ${totalBots}`,
              `**البوتات النشطة:** ${activeBots}`,
              `**بوتات الموسيقى:** ${musicBots}`,
              `**بوتات أخرى:** ${otherBots}`,
              `**التوكنات المتاحة:** ${availableTokens}`
            ].join('\n'),
            inline: false
          },
          {
            name: "⚙️ حالة النظام",
            value: [
              `**استخدام المعالج:** ${systemStats.cpuUsage}`,
              `**استخدام الذاكرة:** ${systemStats.memoryUsage}`,
              `**وقت التشغيل:** ${systemStats.uptime}`,
              `**إجمالي العمليات:** ${systemStats.totalProcesses}`,
              `**العمليات النشطة:** ${systemStats.onlineProcesses}`
            ].join('\n'),
            inline: false
          }
        ])
        .setFooter({ text: `تم التحديث في: ${new Date().toLocaleString()}` })
        .setTimestamp();

      // إضافة معلومات إضافية للمسؤولين فقط
      if (isAdmin) {
        // إضافة معلومات عن أكثر المستخدمين نشاطاً
        let userStats = [];
        for (const [userId, userSubs] of Object.entries(subsData)) {
          if (Array.isArray(userSubs)) {
            const activeUserSubs = userSubs.filter(sub => sub.endDate > Date.now() && sub.ended !== true);
            if (activeUserSubs.length > 0) {
              let totalUserBots = 0;
              for (const sub of activeUserSubs) {
                if (sub.bots && Array.isArray(sub.bots)) {
                  totalUserBots += sub.bots.length;
                }
              }

              userStats.push({
                userId,
                subscriptions: activeUserSubs.length,
                bots: totalUserBots
              });
            }
          }
        }

        // ترتيب المستخدمين حسب عدد البوتات
        userStats.sort((a, b) => b.bots - a.bots);

        // إضافة أعلى 5 مستخدمين
        if (userStats.length > 0) {
          const topUsers = userStats.slice(0, 5).map((user, index) =>
            `**${index + 1}.** <@${user.userId}> - ${user.subscriptions} اشتراك، ${user.bots} بوت`
          ).join('\n');

          statsEmbed.addFields({
            name: "👑 أكثر المستخدمين نشاطاً",
            value: topUsers,
            inline: false
          });
        }
      }

      // تحديث الرسالة بالإحصائيات
      await loadingMsg.edit({ embeds: [statsEmbed] });

    } catch (error) {
      console.error("Error generating stats:", error);

      // إنشاء رسالة خطأ
      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ خطأ في جمع الإحصائيات")
        .setDescription(`حدث خطأ أثناء محاولة جمع إحصائيات النظام: ${error.message}`)
        .setTimestamp();

      await loadingMsg.edit({ embeds: [errorEmbed] });
    }
  } else if (args[0] === prefix + "report") {
    // التحقق من صلاحيات المستخدم (للمسؤولين فقط)
    if (!config.devs.find(u => u === message.author.id))
      return message.reply({ content: `❌ هذا الأمر متاح للمسؤولين فقط.` });

    // تحديد نوع التقرير المطلوب
    const reportType = args[1]?.toLowerCase();
    const validReportTypes = ['system', 'music', 'usage', 'all'];

    if (!reportType || !validReportTypes.includes(reportType)) {
      const helpEmbed = new Discord.EmbedBuilder()
        .setColor("Blue")
        .setTitle("📊 نظام التقارير")
        .setDescription("يرجى تحديد نوع التقرير المطلوب:")
        .addFields([
          { name: `${prefix}report system`, value: "تقرير عن حالة النظام وأداء الخوادم" },
          { name: `${prefix}report music`, value: "تقرير عن أداء بوتات الموسيقى والأغاني الأكثر تشغيلاً" },
          { name: `${prefix}report usage`, value: "تقرير عن استخدام المستخدمين للنظام" },
          { name: `${prefix}report all`, value: "تقرير شامل يتضمن جميع المعلومات" }
        ]);

      return message.reply({ embeds: [helpEmbed] });
    }

    // إنشاء رسالة انتظار
    const loadingEmbed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle("⏳ جاري إنشاء التقرير...")
      .setDescription(`يرجى الانتظار قليلاً بينما نقوم بجمع البيانات لتقرير "${reportType}".`)
      .setTimestamp();

    const loadingMsg = await message.reply({ embeds: [loadingEmbed] });

    try {
      // جمع البيانات الأساسية
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));
      const tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));

      // تقرير حالة النظام
      if (reportType === 'system' || reportType === 'all') {
        // جمع معلومات النظام من PM2
        const pm2List = await new Promise((resolve) => {
          pm2.list((err, list) => {
            if (err) {
              console.error("Error getting PM2 list:", err);
              resolve([]);
            } else {
              resolve(list || []);
            }
          });
        });

        // تحليل بيانات العمليات
        const processStats = {
          total: pm2List.length,
          online: pm2List.filter(p => p.pm2_env?.status === "online").length,
          stopped: pm2List.filter(p => p.pm2_env?.status === "stopped").length,
          errored: pm2List.filter(p => p.pm2_env?.status === "errored").length,
          musicBots: pm2List.filter(p => p.name && p.pm2_env?.env?.subscription_id).length,
          otherBots: pm2List.filter(p => p.name && !p.pm2_env?.env?.subscription_id).length,
          highCpuProcesses: [],
          highMemoryProcesses: []
        };

        // تحديد العمليات التي تستهلك موارد عالية
        for (const process of pm2List) {
          if (process.monit) {
            // العمليات التي تستهلك أكثر من 5% من المعالج
            if (process.monit.cpu > 5) {
              processStats.highCpuProcesses.push({
                name: process.name,
                cpu: process.monit.cpu.toFixed(2) + '%'
              });
            }

            // العمليات التي تستهلك أكثر من 100MB من الذاكرة
            if (process.monit.memory > 100 * 1024 * 1024) {
              processStats.highMemoryProcesses.push({
                name: process.name,
                memory: (process.monit.memory / (1024 * 1024)).toFixed(2) + ' MB'
              });
            }
          }
        }

        // ترتيب العمليات حسب استهلاك الموارد
        processStats.highCpuProcesses.sort((a, b) => parseFloat(b.cpu) - parseFloat(a.cpu));
        processStats.highMemoryProcesses.sort((a, b) => parseFloat(b.memory) - parseFloat(a.memory));

        // إنشاء تقرير حالة النظام
        const systemReportEmbed = new Discord.EmbedBuilder()
          .setColor("#00FFFF")
          .setTitle("📊 تقرير حالة النظام")
          .setDescription(`تقرير مفصل عن حالة النظام وأداء العمليات.`)
          .addFields([
            {
              name: "📈 حالة العمليات",
              value: [
                `**إجمالي العمليات:** ${processStats.total}`,
                `**العمليات النشطة:** ${processStats.online}`,
                `**العمليات المتوقفة:** ${processStats.stopped}`,
                `**العمليات المتعطلة:** ${processStats.errored}`,
                `**بوتات الموسيقى:** ${processStats.musicBots}`,
                `**بوتات أخرى:** ${processStats.otherBots}`
              ].join('\n'),
              inline: false
            }
          ])
          .setTimestamp();

        // إضافة معلومات عن العمليات التي تستهلك موارد عالية
        if (processStats.highCpuProcesses.length > 0) {
          systemReportEmbed.addFields({
            name: "🔥 العمليات ذات استهلاك المعالج العالي",
            value: processStats.highCpuProcesses.slice(0, 5).map(p =>
              `**${p.name}:** ${p.cpu}`
            ).join('\n') || "لا توجد عمليات ذات استهلاك عالٍ للمعالج",
            inline: false
          });
        }

        if (processStats.highMemoryProcesses.length > 0) {
          systemReportEmbed.addFields({
            name: "💾 العمليات ذات استهلاك الذاكرة العالي",
            value: processStats.highMemoryProcesses.slice(0, 5).map(p =>
              `**${p.name}:** ${p.memory}`
            ).join('\n') || "لا توجد عمليات ذات استهلاك عالٍ للذاكرة",
            inline: false
          });
        }

        // إضافة معلومات عن التوكنات
        const availableTokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens)
          ? tokensData.tokens.length
          : 0;

        systemReportEmbed.addFields({
          name: "🔑 حالة التوكنات",
          value: [
            `**التوكنات المتاحة:** ${availableTokens}`,
            `**التوكنات المستخدمة:** ${processStats.total - availableTokens}`
          ].join('\n'),
          inline: false
        });

        // إرسال تقرير حالة النظام
        if (reportType === 'system') {
          await loadingMsg.edit({ embeds: [systemReportEmbed] });
        }
      }

      // تقرير بوتات الموسيقى والأغاني
      if (reportType === 'music' || reportType === 'all') {
        // محاولة قراءة بيانات الموسيقى
        let musicData = {};
        try {
          if (fs.existsSync('projects/music/databases/music.json')) {
            musicData = JSON.parse(fs.readFileSync('projects/music/databases/music.json', 'utf8'));
          }
        } catch (error) {
          console.error("Error reading music data:", error);
        }

        // جمع إحصائيات بوتات الموسيقى
        let musicBots = 0;
        let activeMusicBots = 0;
        let totalSongsPlayed = 0;
        let totalPlaytime = 0;
        let songStats = {};

        // تحليل بيانات الاشتراكات للحصول على معلومات بوتات الموسيقى
        if (subsData && typeof subsData === 'object') {
          for (const [userId, userSubs] of Object.entries(subsData)) {
            if (Array.isArray(userSubs)) {
              for (const sub of userSubs) {
                // التحقق من أن الاشتراك من نوع موسيقى
                const isMusicSub = sub.type === "music" || sub.bots?.find(bot => bot.type?.toLowerCase() === "music");

                if (isMusicSub) {
                  // عدد بوتات الموسيقى
                  const subMusicBots = sub.bots?.filter(bot => bot.type?.toLowerCase() === "music") || [];
                  musicBots += subMusicBots.length;

                  // التحقق من حالة الاشتراك
                  const isActive = sub.endDate > Date.now() && sub.ended !== true;
                  if (isActive) {
                    activeMusicBots += subMusicBots.length;
                  }

                  // جمع إحصائيات الأغاني إذا كانت متوفرة في بيانات الموسيقى
                  for (const bot of subMusicBots) {
                    if (bot.botId && musicData[bot.botId]) {
                      const botStats = musicData[bot.botId];

                      // إحصائيات الأغاني المشغلة
                      if (botStats.songsPlayed) {
                        totalSongsPlayed += botStats.songsPlayed;
                      }

                      // وقت التشغيل
                      if (botStats.playTime) {
                        totalPlaytime += botStats.playTime;
                      }

                      // إحصائيات الأغاني الفردية
                      if (botStats.songs && Array.isArray(botStats.songs)) {
                        for (const song of botStats.songs) {
                          if (song.title) {
                            if (!songStats[song.title]) {
                              songStats[song.title] = {
                                count: 0,
                                duration: 0,
                                url: song.url || null
                              };
                            }
                            songStats[song.title].count += song.playCount || 1;
                            songStats[song.title].duration += song.duration || 0;
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }

        // تحويل إحصائيات الأغاني إلى مصفوفة وترتيبها
        const topSongs = Object.entries(songStats)
          .map(([title, stats]) => ({
            title,
            count: stats.count,
            duration: stats.duration,
            url: stats.url
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 10);

        // إنشاء تقرير الموسيقى
        const musicReportEmbed = new Discord.EmbedBuilder()
          .setColor("#9370DB")
          .setTitle("🎵 تقرير بوتات الموسيقى")
          .setDescription(`تقرير مفصل عن أداء بوتات الموسيقى والأغاني الأكثر تشغيلاً.`)
          .addFields([
            {
              name: "🤖 إحصائيات البوتات",
              value: [
                `**إجمالي بوتات الموسيقى:** ${musicBots}`,
                `**بوتات الموسيقى النشطة:** ${activeMusicBots}`,
                `**إجمالي الأغاني المشغلة:** ${totalSongsPlayed}`,
                `**إجمالي وقت التشغيل:** ${prettyMilliseconds(totalPlaytime)}`
              ].join('\n'),
              inline: false
            }
          ])
          .setTimestamp();

        // إضافة قائمة الأغاني الأكثر تشغيلاً
        if (topSongs.length > 0) {
          musicReportEmbed.addFields({
            name: "🎧 الأغاني الأكثر تشغيلاً",
            value: topSongs.map((song, index) =>
              `**${index + 1}.** ${song.title} - ${song.count} مرة`
            ).join('\n') || "لا توجد بيانات عن الأغاني المشغلة",
            inline: false
          });
        } else {
          musicReportEmbed.addFields({
            name: "🎧 الأغاني الأكثر تشغيلاً",
            value: "لا توجد بيانات كافية عن الأغاني المشغلة",
            inline: false
          });
        }

        // إرسال تقرير الموسيقى
        if (reportType === 'music') {
          await loadingMsg.edit({ embeds: [musicReportEmbed] });
        }
      }

      // تقرير استخدام المستخدمين
      if (reportType === 'usage' || reportType === 'all') {
        // إحصائيات المستخدمين
        let userStats = [];
        let totalUsers = 0;
        let activeUsers = 0;
        let totalSubscriptions = 0;
        let activeSubscriptions = 0;
        let subscriptionsByType = {};

        // تحليل بيانات الاشتراكات
        if (subsData && typeof subsData === 'object') {
          totalUsers = Object.keys(subsData).length;

          for (const [userId, userSubs] of Object.entries(subsData)) {
            if (Array.isArray(userSubs)) {
              totalSubscriptions += userSubs.length;

              // التحقق من الاشتراكات النشطة
              const activeUserSubs = userSubs.filter(sub => sub.endDate > Date.now() && sub.ended !== true);
              if (activeUserSubs.length > 0) {
                activeUsers++;
                activeSubscriptions += activeUserSubs.length;

                // جمع إحصائيات المستخدم
                let totalUserBots = 0;
                let userBotTypes = {};

                for (const sub of activeUserSubs) {
                  // تصنيف الاشتراكات حسب النوع
                  let subType = "other";

                  if (sub.type === "music" || sub.bots?.find(bot => bot.type?.toLowerCase() === "music")) {
                    subType = "music";
                  }

                  if (!subscriptionsByType[subType]) {
                    subscriptionsByType[subType] = 0;
                  }
                  subscriptionsByType[subType]++;

                  // عدد البوتات
                  if (sub.bots && Array.isArray(sub.bots)) {
                    totalUserBots += sub.bots.length;

                    // تصنيف البوتات حسب النوع
                    for (const bot of sub.bots) {
                      const botType = bot.type?.toLowerCase() || "other";
                      if (!userBotTypes[botType]) {
                        userBotTypes[botType] = 0;
                      }
                      userBotTypes[botType]++;
                    }
                  }
                }

                // إضافة إحصائيات المستخدم
                userStats.push({
                  userId,
                  subscriptions: activeUserSubs.length,
                  bots: totalUserBots,
                  botTypes: userBotTypes,
                  oldestSubscription: Math.min(...activeUserSubs.map(sub => sub.startDate)),
                  newestSubscription: Math.max(...activeUserSubs.map(sub => sub.startDate))
                });
              }
            }
          }
        }

        // ترتيب المستخدمين حسب عدد البوتات
        userStats.sort((a, b) => b.bots - a.bots);

        // حساب متوسط عمر الاشتراكات
        const now = Date.now();
        const subscriptionAges = userStats.flatMap(user => [
          now - user.oldestSubscription,
          now - user.newestSubscription
        ]);

        const avgSubscriptionAge = subscriptionAges.length > 0
          ? prettyMilliseconds(subscriptionAges.reduce((sum, age) => sum + age, 0) / subscriptionAges.length)
          : "غير متاح";

        // إنشاء تقرير الاستخدام
        const usageReportEmbed = new Discord.EmbedBuilder()
          .setColor("#FFA500")
          .setTitle("👥 تقرير استخدام المستخدمين")
          .setDescription(`تقرير مفصل عن استخدام المستخدمين للنظام.`)
          .addFields([
            {
              name: "📊 إحصائيات المستخدمين",
              value: [
                `**إجمالي المستخدمين:** ${totalUsers}`,
                `**المستخدمين النشطين:** ${activeUsers}`,
                `**إجمالي الاشتراكات:** ${totalSubscriptions}`,
                `**الاشتراكات النشطة:** ${activeSubscriptions}`,
                `**متوسط عمر الاشتراكات:** ${avgSubscriptionAge}`
              ].join('\n'),
              inline: false
            }
          ])
          .setTimestamp();

        // إضافة معلومات عن أنواع الاشتراكات
        const subscriptionTypesText = Object.entries(subscriptionsByType)
          .map(([type, count]) => `**${type}:** ${count}`)
          .join('\n');

        if (subscriptionTypesText) {
          usageReportEmbed.addFields({
            name: "🔖 أنواع الاشتراكات",
            value: subscriptionTypesText,
            inline: false
          });
        }

        // إضافة أكثر المستخدمين نشاطاً
        if (userStats.length > 0) {
          const topUsers = userStats.slice(0, 5).map((user, index) => {
            const botTypesText = Object.entries(user.botTypes)
              .map(([type, count]) => `${type}: ${count}`)
              .join(', ');

            return `**${index + 1}.** <@${user.userId}> - ${user.subscriptions} اشتراك، ${user.bots} بوت (${botTypesText})`;
          }).join('\n');

          usageReportEmbed.addFields({
            name: "👑 أكثر المستخدمين نشاطاً",
            value: topUsers,
            inline: false
          });
        }

        // إرسال تقرير الاستخدام
        if (reportType === 'usage') {
          await loadingMsg.edit({ embeds: [usageReportEmbed] });
        }
      }

      // إرسال التقرير الشامل
      if (reportType === 'all') {
        // إنشاء رسالة التقرير الشامل
        const allReportEmbed = new Discord.EmbedBuilder()
          .setColor("#00FF00")
          .setTitle("📑 التقرير الشامل")
          .setDescription(`تم إنشاء التقرير الشامل بنجاح. يرجى الاطلاع على التقارير المفصلة أدناه.`)
          .setTimestamp();

        await loadingMsg.edit({ embeds: [allReportEmbed] });

        // إرسال التقارير المفصلة في رسائل منفصلة
        const systemReportEmbed = new Discord.EmbedBuilder()
          .setColor("#00FFFF")
          .setTitle("📊 تقرير حالة النظام")
          .setDescription(`للحصول على تقرير مفصل عن حالة النظام، استخدم الأمر: \`${prefix}report system\``)
          .setTimestamp();

        const musicReportEmbed = new Discord.EmbedBuilder()
          .setColor("#9370DB")
          .setTitle("🎵 تقرير بوتات الموسيقى")
          .setDescription(`للحصول على تقرير مفصل عن بوتات الموسيقى، استخدم الأمر: \`${prefix}report music\``)
          .setTimestamp();

        const usageReportEmbed = new Discord.EmbedBuilder()
          .setColor("#FFA500")
          .setTitle("👥 تقرير استخدام المستخدمين")
          .setDescription(`للحصول على تقرير مفصل عن استخدام المستخدمين، استخدم الأمر: \`${prefix}report usage\``)
          .setTimestamp();

        await message.channel.send({ embeds: [systemReportEmbed] });
        await message.channel.send({ embeds: [musicReportEmbed] });
        await message.channel.send({ embeds: [usageReportEmbed] });
      }

    } catch (error) {
      console.error("Error generating report:", error);

      // إنشاء رسالة خطأ
      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ خطأ في إنشاء التقرير")
        .setDescription(`حدث خطأ أثناء محاولة إنشاء التقرير: ${error.message}`)
        .setTimestamp();

      await loadingMsg.edit({ embeds: [errorEmbed] });
    }
  } else if (args[0] === prefix + "help") {
    // إنشاء Embed للمساعدة
    const helpEmbed = new Discord.EmbedBuilder()
      .setColor("#0099ff")
      .setTitle("📚 دليل الأوامر")
      .setDescription(`مرحباً بك في نظام إدارة البوتات. فيما يلي قائمة بالأوامر المتاحة:`)
      .setThumbnail(client.user.displayAvatarURL({ dynamic: true }))
      .setFooter({ text: `استخدم ${prefix} قبل كل أمر` });

    // إضافة أوامر المستخدمين العاديين
    const userCommands = [
      { name: `${prefix}help`, value: "عرض قائمة الأوامر المتاحة" },
      { name: `${prefix}panel`, value: "🎛️ لوحة التحكم التفاعلية - شراء وإدارة الاشتراكات" },
      { name: `${prefix}mysub`, value: "عرض اشتراكاتك وإدارتها (إعادة تشغيل، نقل السيرفر، نقل الملكية)" },
      { name: `${prefix}mylinks [معرف الاشتراك]`, value: "الحصول على روابط دعوة البوتات الخاصة باشتراك معين" },
      { name: `${prefix}setserver [معرف الاشتراك] [معرف السيرفر]`, value: "تغيير السيرفر الذي تعمل فيه البوتات" },
      { name: `${prefix}stats`, value: "عرض إحصائيات النظام والاشتراكات" }
    ];

    helpEmbed.addFields({ name: "🔹 أوامر المستخدمين", value: "\u200B" });
    userCommands.forEach(cmd => {
      helpEmbed.addFields({ name: cmd.name, value: cmd.value, inline: false });
    });

    // إضافة أوامر المسؤولين إذا كان المستخدم مسؤولاً
    if (config.devs.find(u => u === message.author.id)) {
      const adminCommands = [
        { name: `${prefix}add [@مستخدم] [معرف السيرفر] [المدة]`, value: "إضافة اشتراك جديد" },
        { name: `${prefix}add-music [@مستخدم] [معرف السيرفر] [المدة] [العدد]`, value: "إضافة اشتراك بوتات موسيقى" },
        { name: `${prefix}end [@مستخدم] [معرف الاشتراك]`, value: "إنهاء اشتراك" },
        { name: `${prefix}renew [@مستخدم] [معرف الاشتراك] [المدة]`, value: "تجديد اشتراك" },
        { name: `${prefix}token [التوكن]`, value: "إضافة توكن جديد للمخزون" },
        { name: `${prefix}tokens`, value: "عرض عدد التوكنات المتوفرة في المخزون" },
        { name: `${prefix}delete [inactive/all]`, value: "حذف الاشتراكات غير النشطة أو جميع الاشتراكات" },
        { name: `${prefix}stats`, value: "عرض إحصائيات مفصلة عن النظام والاشتراكات" },
        { name: `${prefix}report [system/music/usage/all]`, value: "إنشاء تقارير مفصلة عن حالة النظام وأداء البوتات والاستخدام" }
      ];

      helpEmbed.addFields({ name: "🔸 أوامر المسؤولين", value: "\u200B" });
      adminCommands.forEach(cmd => {
        helpEmbed.addFields({ name: cmd.name, value: cmd.value, inline: false });
      });
    }

    // إضافة أمثلة على الاستخدام
    helpEmbed.addFields({
      name: "📝 أمثلة على الاستخدام",
      value: `${prefix}mysub - لعرض اشتراكاتك\n${prefix}mylinks abc123 - لعرض روابط الاشتراك abc123\n${prefix}setserver abc123 987654321 - لنقل بوتات الاشتراك abc123 إلى السيرفر 987654321`
    });

    // إرسال رسالة المساعدة
    message.reply({ embeds: [helpEmbed] });
  } else if (args[0] === prefix + "panel") {
    // يمكن لأي شخص إنشاء اللوحة (أزل التعليق أدناه إذا كنت تريد تقييد الأمر للمسؤولين فقط)
    // if (!config.owners.includes(message.author.id)) {
    //   return message.reply({ content: "❌ هذا الأمر مخصص للمسؤولين فقط." });
    // }

    // لوحة التحكم الثابتة للجميع
    const panelEmbed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle("Berlin Smart System")
      .addFields([
        {
          name: "<:shoppingcart1:1376821025752485959> الشراء",
          value: "اضغط على زر الشراء أدناه لبدء عملية شراء جديدة",
          inline: false
        },
        {
          name: "<:settings:1376821222121144320> التحكم",
          value: `اضغط على زر التحكم لعرض وإدارة اشتراكاتك الحالية`,
          inline: false
        },

        {
          name: "<:megaphone:1376823901446864917> معلومات",
          value: "• مدة الاشتراك: 30 يوم\n• الحد الأقصى للبوتات: 20 بوت\n• نوع الاشتراك المتاح: ميوزك",
          inline: false
        }
      ])
      .setFooter({ text: "الخدمة مقدمة بشكل مجاني لمدة مؤقتة ولغرض التجربة, قبل الاشتراك في الخدمة نرجو مراجعة شروط الخدمة" })

    // إنشاء أزرار اللوحة الثابتة
    const purchaseButton = new Discord.ButtonBuilder()
      .setCustomId('global_panel_purchase')
      .setLabel('الشراء')
       .setEmoji('1376821025752485959')
      .setStyle(Discord.ButtonStyle.Secondary);

    const mySubsButton = new Discord.ButtonBuilder()
      .setCustomId('global_panel_mysubs')
      .setLabel('التحكم')
       .setEmoji('1376821222121144320')
      .setStyle(Discord.ButtonStyle.Secondary);

    const row = new Discord.ActionRowBuilder().addComponents(
      purchaseButton,
      mySubsButton
    );

    // إرسال اللوحة الثابتة
    await message.channel.send({ embeds: [panelEmbed], components: [row] });

    // حذف رسالة الأمر
    await message.delete().catch(() => {});
  }
});

// معالجة التفاعلات العامة للوحة الثابتة
client.on('interactionCreate', async interaction => {
  if (!interaction.isButton() && !interaction.isModalSubmit()) return;

  try {
    // معالجة أزرار اللوحة العامة
    if (interaction.customId === 'global_panel_purchase') {
      // بدء عملية الشراء للمستخدم الذي ضغط الزر
      await startPurchaseFlow(interaction, interaction.user);
    } else if (interaction.customId.startsWith('purchase_music_')) {
      // معالجة اختيار اشتراك الموسيقى
      const userId = interaction.customId.replace('purchase_music_', '');
      if (interaction.user.id !== userId) {
        return await interaction.reply({
          content: "❌ هذا الزر مخصص لمستخدم آخر.",
          ephemeral: true
        });
      }
      await handleMusicSubscriptionModal(interaction);
    } else if (interaction.customId.startsWith('purchase_cancel_')) {
      // معالجة إلغاء الشراء
      const userId = interaction.customId.replace('purchase_cancel_', '');
      if (interaction.user.id !== userId) {
        return await interaction.reply({
          content: "❌ هذا الزر مخصص لمستخدم آخر.",
          ephemeral: true
        });
      }

      const cancelEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("تم إلغاء العملية")
        .setDescription("تم إلغاء عملية الشراء.")
        .setFooter({ text: "يمكنك بدء عملية جديدة في أي وقت" });

      await interaction.update({ embeds: [cancelEmbed], components: [] });
    } else if (interaction.customId.startsWith('subscription_details_')) {
      // معالجة إرسال تفاصيل الاشتراك
      const userId = interaction.customId.replace('subscription_details_', '');
      if (interaction.user.id !== userId) {
        return await interaction.reply({
          content: "❌ هذا النموذج مخصص لمستخدم آخر.",
          ephemeral: true
        });
      }
      await handleSubscriptionDetails(interaction);
    } else if (interaction.customId.startsWith('server_transfer_')) {
      // معالجة Modal نقل السيرفر
      const subId = interaction.customId.replace('server_transfer_', '');
      const newServerId = interaction.fields.getTextInputValue('new_server_id');

      // التحقق من صحة معرف السيرفر
      if (!newServerId || isNaN(newServerId) || newServerId.length < 15) {
        return await interaction.reply({
          content: "معرف السيرفر غير صالح. يجب أن يكون مكون من 15-20 رقم.",
          ephemeral: true
        });
      }

      // البحث عن الاشتراك في بيانات المستخدم
      let userData = await db.get(interaction.user.id);
      if (!userData) {
        return await interaction.reply({
          content: "لم يتم العثور على بيانات المستخدم.",
          ephemeral: true
        });
      }

      const subIndex = userData.findIndex(s => s.id === subId);
      if (subIndex === -1) {
        return await interaction.reply({
          content: "لم يتم العثور على الاشتراك المحدد.",
          ephemeral: true
        });
      }

      const subscription = userData[subIndex];

      // التحقق من أن السيرفر الجديد مختلف عن الحالي
      if (newServerId === subscription.guildID) {
        return await interaction.reply({
          content: "هذا هو نفس السيرفر الحالي للاشتراك.",
          ephemeral: true
        });
      }

      // تحديث السيرفر
      try {
        userData[subIndex].guildID = newServerId;
        await db.set(interaction.user.id, userData);

        // تحديث ملف الاشتراكات
        const fs = require('fs');
        const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));
        if (subsData[interaction.user.id]) {
          let fileSubIndex = subsData[interaction.user.id].findIndex(s => s.id === subId);
          if (fileSubIndex !== -1) {
            subsData[interaction.user.id][fileSubIndex].guildID = newServerId;
            fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
          }
        }

        // إعادة تشغيل الاشتراك
        restartSub(userData[subIndex]);

        // عرض رسالة النجاح
        const successEmbed = new Discord.EmbedBuilder()
          .setColor("Green")
          .setTitle("تم نقل السيرفر بنجاح")
          .setDescription("تم نقل الاشتراك إلى السيرفر الجديد وإعادة تشغيله.")
          .addFields([
            {
              name: "تفاصيل النقل",
              value: `معرف الاشتراك: ${subscription.id}\nالسيرفر القديم: ${subscription.guildID}\nالسيرفر الجديد: ${newServerId}\nعدد البوتات: ${subscription.bots.length}`,
              inline: false
            }
          ])
          .setFooter({ text: "ستحصل على روابط البوتات في الخاص" });

        await interaction.reply({ embeds: [successEmbed], ephemeral: true });

        // إرسال الروابط في الخاص
        let links = userData[subIndex].bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${newServerId}&disable_guild_select=true`);
        let n = 10;
        for (let i = 0; i < links.length; i += n) {
          let ee = links.slice(i, i + n);
          await interaction.user.send({ content: `${ee.join("\n")}` }).catch(() => 0);
        }

      } catch (error) {
        console.error("Error updating server:", error);
        await interaction.reply({
          content: "حدث خطأ أثناء محاولة نقل السيرفر. يرجى المحاولة مرة أخرى.",
          ephemeral: true
        });
      }
    } else if (interaction.customId.startsWith('confirm_purchase_modal_')) {
      // معالجة تأكيد الشراء من Modal
      const parts = interaction.customId.replace('confirm_purchase_modal_', '').split('_');
      const userId = parts[0];
      const guildId = parts[1];
      const botCount = parseInt(parts[2]);

      if (interaction.user.id !== userId) {
        return await interaction.reply({
          content: "❌ هذا الزر مخصص لمستخدم آخر.",
          ephemeral: true
        });
      }

      await processPurchaseModal(interaction, interaction.user, guildId, botCount);
    } else if (interaction.customId.startsWith('cancel_purchase_modal_')) {
      // معالجة إلغاء الشراء من Modal
      const userId = interaction.customId.replace('cancel_purchase_modal_', '');
      if (interaction.user.id !== userId) {
        return await interaction.reply({
          content: "❌ هذا الزر مخصص لمستخدم آخر.",
          ephemeral: true
        });
      }

      const cancelEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("تم إلغاء الشراء")
        .setDescription("تم إلغاء عملية الشراء.")
        .setFooter({ text: "يمكنك بدء عملية جديدة في أي وقت" });

      await interaction.update({ embeds: [cancelEmbed], components: [] });
    } else if (interaction.customId === 'global_panel_mysubs') {
      // عرض واجهة mysub مباشرة
      await showMySubsInterface(interaction);
    } else if (interaction.customId.startsWith('panel_confirm_restart_')) {
      // معالجة تأكيد إعادة التشغيل من اللوحة
      const subId = interaction.customId.replace('panel_confirm_restart_', '');

      // البحث عن الاشتراك
      let userData = await db.get(interaction.user.id);
      if (!userData) {
        return await interaction.reply({
          content: "لم يتم العثور على بيانات المستخدم.",
          ephemeral: true
        });
      }

      const subscription = userData.find(sub => sub.id === subId);
      if (!subscription) {
        return await interaction.reply({
          content: "لم يتم العثور على الاشتراك المحدد.",
          ephemeral: true
        });
      }

      // إعادة تشغيل الاشتراك
      try {
        restartSub(subscription);

        const successEmbed = new Discord.EmbedBuilder()
          .setColor("Green")
          .setTitle("تم إعادة التشغيل بنجاح")
          .setDescription("تم إعادة تشغيل جميع البوتات في الاشتراك.")
          .addFields([
            {
              name: "تفاصيل الاشتراك",
              value: `معرف الاشتراك: ${subscription.id}\nعدد البوتات: ${subscription.bots.length}\nالسيرفر: ${subscription.guildID}`,
              inline: false
            }
          ])
          .setFooter({ text: "تم إعادة تشغيل جميع البوتات بنجاح" });

        await interaction.update({ embeds: [successEmbed], components: [] });

      } catch (error) {
        console.error("Error restarting subscription:", error);
        await interaction.reply({
          content: "حدث خطأ أثناء إعادة تشغيل الاشتراك.",
          ephemeral: true
        });
      }
    } else if (interaction.customId.startsWith('panel_add_server_id_')) {
      // معالجة زر إضافة معرف السيرفر من اللوحة
      const subId = interaction.customId.replace('panel_add_server_id_', '');

      // إنشاء Modal لإدخال معرف السيرفر
      const modal = new Discord.ModalBuilder()
        .setCustomId(`panel_server_transfer_${subId}`)
        .setTitle('نقل السيرفر');

      // حقل معرف السيرفر الجديد
      const serverIdInput = new Discord.TextInputBuilder()
        .setCustomId('new_server_id')
        .setLabel('معرف السيرفر الجديد')
        .setPlaceholder('1234567890123456789')
        .setStyle(Discord.TextInputStyle.Short)
        .setMinLength(15)
        .setMaxLength(20)
        .setRequired(true);

      const serverRow = new Discord.ActionRowBuilder().addComponents(serverIdInput);
      modal.addComponents(serverRow);

      await interaction.showModal(modal);
    } else if (interaction.customId.startsWith('panel_server_transfer_')) {
      // معالجة Modal نقل السيرفر من اللوحة
      const subId = interaction.customId.replace('panel_server_transfer_', '');
      const newServerId = interaction.fields.getTextInputValue('new_server_id');

      // التحقق من صحة معرف السيرفر
      if (!newServerId || isNaN(newServerId) || newServerId.length < 15) {
        return await interaction.reply({
          content: "معرف السيرفر غير صالح. يجب أن يكون مكون من 15-20 رقم.",
          ephemeral: true
        });
      }

      // البحث عن الاشتراك في بيانات المستخدم
      let userData = await db.get(interaction.user.id);
      if (!userData) {
        return await interaction.reply({
          content: "لم يتم العثور على بيانات المستخدم.",
          ephemeral: true
        });
      }

      const subIndex = userData.findIndex(s => s.id === subId);
      if (subIndex === -1) {
        return await interaction.reply({
          content: "لم يتم العثور على الاشتراك المحدد.",
          ephemeral: true
        });
      }

      const subscription = userData[subIndex];

      // التحقق من أن السيرفر الجديد مختلف عن الحالي
      if (newServerId === subscription.guildID) {
        return await interaction.reply({
          content: "هذا هو نفس السيرفر الحالي للاشتراك.",
          ephemeral: true
        });
      }

      // تحديث السيرفر
      try {
        userData[subIndex].guildID = newServerId;
        await db.set(interaction.user.id, userData);

        // تحديث ملف الاشتراكات
        const fs = require('fs');
        const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));
        if (subsData[interaction.user.id]) {
          let fileSubIndex = subsData[interaction.user.id].findIndex(s => s.id === subId);
          if (fileSubIndex !== -1) {
            subsData[interaction.user.id][fileSubIndex].guildID = newServerId;
            fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
          }
        }

        // إعادة تشغيل الاشتراك
        restartSub(userData[subIndex]);

        // عرض رسالة النجاح
        const successEmbed = new Discord.EmbedBuilder()
          .setColor("Green")
          .setTitle("تم نقل السيرفر بنجاح")
          .setDescription("تم نقل الاشتراك إلى السيرفر الجديد وإعادة تشغيله.")
          .addFields([
            {
              name: "تفاصيل النقل",
              value: `معرف الاشتراك: ${subscription.id}\nالسيرفر القديم: ${subscription.guildID}\nالسيرفر الجديد: ${newServerId}\nعدد البوتات: ${subscription.bots.length}`,
              inline: false
            }
          ])
          .setFooter({ text: "ستحصل على روابط البوتات في الخاص" });

        await interaction.reply({ embeds: [successEmbed], ephemeral: true });

        // إرسال الروابط في الخاص
        let links = userData[subIndex].bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${newServerId}&disable_guild_select=true`);
        let n = 10;
        for (let i = 0; i < links.length; i += n) {
          let ee = links.slice(i, i + n);
          await interaction.user.send({ content: `${ee.join("\n")}` }).catch(() => 0);
        }

      } catch (error) {
        console.error("Error updating server from panel:", error);
        await interaction.reply({
          content: "حدث خطأ أثناء محاولة نقل السيرفر. يرجى المحاولة مرة أخرى.",
          ephemeral: true
        });
      }
    } else if (interaction.customId.startsWith('panel_ownership_transfer_')) {
      // معالجة Modal نقل الملكية من اللوحة
      const subId = interaction.customId.replace('panel_ownership_transfer_', '');
      const newOwnerId = interaction.fields.getTextInputValue('new_owner_id');

      // التحقق من صحة معرف المستخدم
      if (!newOwnerId || isNaN(newOwnerId) || newOwnerId.length < 15) {
        return await interaction.reply({
          content: "معرف المستخدم غير صالح. يجب أن يكون مكون من 15-20 رقم.",
          ephemeral: true
        });
      }

      // التحقق من أن المستخدم الجديد ليس نفس المالك الحالي
      if (newOwnerId === interaction.user.id) {
        return await interaction.reply({
          content: "لا يمكنك نقل الاشتراك إلى نفسك.",
          ephemeral: true
        });
      }

      // البحث عن الاشتراك في بيانات المستخدم
      let userData = await db.get(interaction.user.id);
      if (!userData) {
        return await interaction.reply({
          content: "لم يتم العثور على بيانات المستخدم.",
          ephemeral: true
        });
      }

      const subIndex = userData.findIndex(s => s.id === subId);
      if (subIndex === -1) {
        return await interaction.reply({
          content: "لم يتم العثور على الاشتراك المحدد.",
          ephemeral: true
        });
      }

      const subscription = userData[subIndex];

      // التحقق من وجود المستخدم الجديد
      try {
        const newOwner = await client.users.fetch(newOwnerId);

        // عرض تأكيد نقل الملكية
        const confirmEmbed = new Discord.EmbedBuilder()
          .setColor("Orange")
          .setTitle("تأكيد نقل الملكية")
          .setDescription("هل تريد نقل ملكية هذا الاشتراك؟")
          .addFields([
            {
              name: "تفاصيل الاشتراك",
              value: `معرف الاشتراك: ${subscription.id}\nعدد البوتات: ${subscription.bots.length}\nالسيرفر: ${subscription.guildID}`,
              inline: false
            },
            {
              name: "المالك الحالي",
              value: `<@${interaction.user.id}>`,
              inline: true
            },
            {
              name: "المالك الجديد",
              value: `<@${newOwnerId}> (${newOwner.username})`,
              inline: true
            },
            {
              name: "تحذير مهم",
              value: "بعد التأكيد، لن تتمكن من الوصول إلى هذا الاشتراك مرة أخرى.",
              inline: false
            }
          ])
          .setFooter({ text: "تأكد من صحة البيانات قبل التأكيد" });

        const confirmButton = new Discord.ButtonBuilder()
          .setCustomId(`panel_confirm_ownership_${subId}_${newOwnerId}`)
          .setLabel("تأكيد نقل الملكية")
          .setStyle(Discord.ButtonStyle.Danger);

        const cancelButton = new Discord.ButtonBuilder()
          .setCustomId('panel_back_to_main')
          .setLabel("إلغاء")
          .setStyle(Discord.ButtonStyle.Secondary);

        const confirmRow = new Discord.ActionRowBuilder().addComponents(confirmButton, cancelButton);

        await interaction.reply({ embeds: [confirmEmbed], components: [confirmRow], ephemeral: true });

      } catch (error) {
        console.error("Error fetching new owner:", error);
        await interaction.reply({
          content: "لم يتم العثور على المستخدم المحدد. تأكد من صحة معرف المستخدم.",
          ephemeral: true
        });
      }
    } else if (interaction.customId.startsWith('panel_confirm_ownership_')) {
      // معالجة تأكيد نقل الملكية
      const parts = interaction.customId.replace('panel_confirm_ownership_', '').split('_');
      const subId = parts[0];
      const newOwnerId = parts[1];

      try {
        // البحث عن الاشتراك
        let userData = await db.get(interaction.user.id);
        if (!userData) {
          return await interaction.reply({
            content: "لم يتم العثور على بيانات المستخدم.",
            ephemeral: true
          });
        }

        const subIndex = userData.findIndex(s => s.id === subId);
        if (subIndex === -1) {
          return await interaction.reply({
            content: "لم يتم العثور على الاشتراك المحدد.",
            ephemeral: true
          });
        }

        const subscription = userData[subIndex];

        // إزالة الاشتراك من المالك الحالي
        userData.splice(subIndex, 1);
        await db.set(interaction.user.id, userData);

        // إضافة الاشتراك للمالك الجديد
        let newOwnerData = await db.get(newOwnerId) || [];
        newOwnerData.push(subscription);
        await db.set(newOwnerId, newOwnerData);

        // تحديث ملف الاشتراكات
        const fs = require('fs');
        const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

        // إزالة من المالك القديم
        if (subsData[interaction.user.id]) {
          const fileSubIndex = subsData[interaction.user.id].findIndex(s => s.id === subId);
          if (fileSubIndex !== -1) {
            subsData[interaction.user.id].splice(fileSubIndex, 1);
            if (subsData[interaction.user.id].length === 0) {
              delete subsData[interaction.user.id];
            }
          }
        }

        // إضافة للمالك الجديد
        if (!subsData[newOwnerId]) {
          subsData[newOwnerId] = [];
        }
        subsData[newOwnerId].push(subscription);

        fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));

        // عرض رسالة النجاح
        const successEmbed = new Discord.EmbedBuilder()
          .setColor("Green")
          .setTitle("تم نقل الملكية بنجاح")
          .setDescription("تم نقل ملكية الاشتراك بنجاح.")
          .addFields([
            {
              name: "تفاصيل النقل",
              value: `معرف الاشتراك: ${subscription.id}\nالمالك السابق: <@${interaction.user.id}>\nالمالك الجديد: <@${newOwnerId}>\nعدد البوتات: ${subscription.bots.length}`,
              inline: false
            }
          ])
          .setFooter({ text: "تم نقل الملكية بنجاح" });

        await interaction.update({ embeds: [successEmbed], components: [] });

        // إشعار المالك الجديد
        try {
          const newOwner = await client.users.fetch(newOwnerId);
          const notificationEmbed = new Discord.EmbedBuilder()
            .setColor("Blue")
            .setTitle("تم نقل اشتراك إليك")
            .setDescription(`تم نقل ملكية اشتراك إليك من <@${interaction.user.id}>`)
            .addFields([
              {
                name: "تفاصيل الاشتراك",
                value: `معرف الاشتراك: ${subscription.id}\nعدد البوتات: ${subscription.bots.length}\nالسيرفر: ${subscription.guildID}`,
                inline: false
              }
            ])
            .setFooter({ text: "يمكنك إدارة الاشتراك من خلال اللوحة" });

          await newOwner.send({ embeds: [notificationEmbed] }).catch(() => {
            console.log(`Could not send notification to new owner ${newOwnerId}`);
          });
        } catch (error) {
          console.error("Error sending notification to new owner:", error);
        }

      } catch (error) {
        console.error("Error transferring ownership:", error);
        await interaction.reply({
          content: "حدث خطأ أثناء نقل الملكية. يرجى المحاولة مرة أخرى.",
          ephemeral: true
        });
      }
    }
  } catch (error) {
    console.error('Error in global panel interaction:', error);

    // معالجة الأخطاء بحذر
    try {
      if (!interaction.replied && !interaction.deferred) {
        await interaction.reply({
          content: 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
          ephemeral: true
        });
      } else if (interaction.deferred) {
        await interaction.editReply({
          content: 'حدث خطأ أثناء معالجة طلبك. يرجى المحاولة مرة أخرى.',
          embeds: [],
          components: []
        });
      }
    } catch (replyError) {
      console.error('Error sending error message:', replyError);
    }
  }
});

// دالة عرض واجهة اشتراكاتي
async function showMySubsInterface(interaction) {
  try {
    const user = interaction.user;
    let data = await db.get(user.id);

    if (!data || data.length < 1) {
      return await interaction.reply({
        content: `ليس لديك أي اشتراك.`,
        ephemeral: true
      });
    }

    // تصفية الاشتراكات النشطة فقط
    let activeSubscriptions = data.filter(sub => sub.endDate > Date.now() && sub.ended !== true);

    if (activeSubscriptions.length === 0) {
      return await interaction.reply({
        content: `ليس لديك اشتراكات نشطة حالياً.`,
        ephemeral: true
      });
    }

    // إنشاء قائمة الاشتراكات
    const subscriptionOptions = activeSubscriptions.map((sub, index) => {
      const subType = sub.type === "music" || sub.bots.find(bot => bot.type?.toLowerCase() === "music") ? "موسيقى" : "عام";
      const timeLeft = prettyMilliseconds(sub.endDate - Date.now());
      return {
        label: `${subType} - ${sub.bots.length} بوت`,
        description: `معرف: ${sub.id} - ينتهي في ${timeLeft}`,
        value: sub.id
      };
    });

    const subscriptionMenu = new Discord.StringSelectMenuBuilder()
      .setCustomId('panel_select_subscription')
      .setPlaceholder('اختر اشتراكاً لإدارته')
      .addOptions(subscriptionOptions);

    const embed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle(`إدارة الاشتراكات`)
      .setDescription(`لديك **${activeSubscriptions.length}** اشتراك نشط. اختر اشتراكاً من القائمة للتحكم به.`)
            .setFooter({ text: "اختر من القائمة اجراء لإدارة الاشتراك" })
      .setTimestamp();

    const menuRow = new Discord.ActionRowBuilder().addComponents(subscriptionMenu);

    await interaction.reply({ embeds: [embed], components: [menuRow], ephemeral: true });

    // إنشاء مجمع للتفاعلات
    const collector = interaction.channel.createMessageComponentCollector({
      filter: i => i.user.id === user.id && i.customId.startsWith('panel_'),
      time: 300000 // 5 دقائق
    });

    collector.on('collect', async panelInteraction => {
      try {
        // تأجيل الرد فقط للتفاعلات التي لا تحتاج Modal
        const needsModal = panelInteraction.customId.startsWith('panel_add_server_id_') ||
                          panelInteraction.customId.startsWith('panel_add_owner_id_');

        if (!needsModal && !panelInteraction.deferred && !panelInteraction.replied) {
          await panelInteraction.deferUpdate();
        }

        if (panelInteraction.customId === 'panel_select_subscription') {
          const selectedSubId = panelInteraction.values[0];
          const selectedSub = activeSubscriptions.find(sub => sub.id === selectedSubId);

          if (!selectedSub) {
            await panelInteraction.editReply({ content: "لم يتم العثور على الاشتراك المحدد." });
            return;
          }

          await showSubscriptionManagement(panelInteraction, selectedSub, activeSubscriptions, user);
        } else if (panelInteraction.customId === 'panel_action_menu') {
          await handlePanelAction(panelInteraction, activeSubscriptions, user);
        } else if (panelInteraction.customId === 'panel_back_to_main') {
          // العودة للقائمة الرئيسية
          await panelInteraction.editReply({ embeds: [embed], components: [menuRow] });
        } else if (panelInteraction.customId.startsWith('panel_confirm_restart_')) {
          // معالجة تأكيد إعادة التشغيل
          const subId = panelInteraction.customId.replace('panel_confirm_restart_', '');
          const subscription = activeSubscriptions.find(sub => sub.id === subId);

          if (!subscription) {
            await panelInteraction.reply({ content: "لم يتم العثور على الاشتراك المحدد.", ephemeral: true });
            return;
          }

          try {
            restartSub(subscription);

            const successEmbed = new Discord.EmbedBuilder()
              .setColor("Green")
              .setTitle("تم إعادة التشغيل بنجاح")
              .setDescription("تم إعادة تشغيل جميع البوتات في الاشتراك.")
              .addFields([
                {
                  name: "تفاصيل الاشتراك",
                  value: `معرف الاشتراك: ${subscription.id}\nعدد البوتات: ${subscription.bots.length}\nالسيرفر: ${subscription.guildID}`,
                  inline: false
                }
              ])
              .setFooter({ text: "تم إعادة تشغيل جميع البوتات بنجاح" });

            await panelInteraction.editReply({ embeds: [successEmbed], components: [] });

          } catch (error) {
            console.error("Error restarting subscription:", error);
            await panelInteraction.editReply({
              content: "حدث خطأ أثناء إعادة تشغيل الاشتراك.",
              embeds: [],
              components: []
            });
          }
        } else if (panelInteraction.customId.startsWith('panel_add_server_id_')) {
          // معالجة زر إضافة معرف السيرفر
          const subId = panelInteraction.customId.replace('panel_add_server_id_', '');

          // إنشاء Modal لإدخال معرف السيرفر
          const modal = new Discord.ModalBuilder()
            .setCustomId(`panel_server_transfer_${subId}`)
            .setTitle('نقل السيرفر');

          // حقل معرف السيرفر الجديد
          const serverIdInput = new Discord.TextInputBuilder()
            .setCustomId('new_server_id')
            .setLabel('معرف السيرفر الجديد')
            .setPlaceholder('1234567890123456789')
            .setStyle(Discord.TextInputStyle.Short)
            .setMinLength(15)
            .setMaxLength(20)
            .setRequired(true);

          const serverRow = new Discord.ActionRowBuilder().addComponents(serverIdInput);
          modal.addComponents(serverRow);

          await panelInteraction.showModal(modal);
        } else if (panelInteraction.customId.startsWith('panel_add_owner_id_')) {
          // معالجة زر إضافة معرف المستخدم لنقل الملكية
          const subId = panelInteraction.customId.replace('panel_add_owner_id_', '');

          // إنشاء Modal لإدخال معرف المستخدم
          const modal = new Discord.ModalBuilder()
            .setCustomId(`panel_ownership_transfer_${subId}`)
            .setTitle('نقل ملكية الاشتراك');

          // حقل معرف المستخدم الجديد
          const userIdInput = new Discord.TextInputBuilder()
            .setCustomId('new_owner_id')
            .setLabel('معرف المستخدم الجديد')
            .setPlaceholder('123456789012345678')
            .setStyle(Discord.TextInputStyle.Short)
            .setMinLength(15)
            .setMaxLength(20)
            .setRequired(true);

          const userRow = new Discord.ActionRowBuilder().addComponents(userIdInput);
          modal.addComponents(userRow);

          await panelInteraction.showModal(modal);
        }
      } catch (error) {
        console.error('Error in panel mysubs interaction:', error);
        if (!panelInteraction.replied && !panelInteraction.deferred) {
          await panelInteraction.reply({
            content: 'حدث خطأ أثناء معالجة طلبك.',
            ephemeral: true
          }).catch(() => {});
        }
      }
    });

    collector.on('end', () => {
      // تعطيل القائمة عند انتهاء الوقت
      subscriptionMenu.setDisabled(true);
      const disabledRow = new Discord.ActionRowBuilder().addComponents(subscriptionMenu);

      const timeoutEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("انتهت جلسة الإدارة")
        .setDescription("انتهت مهلة إدارة الاشتراكات. استخدم الأمر مرة أخرى للمتابعة.")
        .setFooter({ text: "يمكنك استخدام اللوحة مرة أخرى" })
        .setTimestamp();

      interaction.editReply({ embeds: [timeoutEmbed], components: [disabledRow] }).catch(() => {});
    });

  } catch (error) {
    console.error('Error in showMySubsInterface:', error);
    await interaction.reply({
      content: 'حدث خطأ أثناء عرض الاشتراكات.',
      ephemeral: true
    });
  }
}

// دالة عرض إدارة الاشتراك المحدد
async function showSubscriptionManagement(interaction, subscription, activeSubscriptions, user) {
  try {
    // إنشاء قائمة الإجراءات
    const actionsMenu = new Discord.StringSelectMenuBuilder()
      .setCustomId('panel_action_menu')
      .setPlaceholder('اختر إجراءً للتنفيذ')
      .addOptions([
        {
          label: 'إعادة تشغيل الاشتراك',
          description: 'إعادة تشغيل جميع البوتات في هذا الاشتراك',
          value: `restart_${subscription.id}`
        },
        {
          label: 'نقل البوتات إلى سيرفر جديد',
          description: 'تغيير السيرفر الذي تعمل فيه البوتات',
          value: `transfer_server_${subscription.id}`
        },
        {
          label: 'نقل ملكية الاشتراك',
          description: 'نقل ملكية الاشتراك إلى مستخدم آخر',
          value: `transfer_ownership_${subscription.id}`
        },
        {
          label: 'عرض روابط البوتات',
          description: 'الحصول على روابط دعوة البوتات',
          value: `show_links_${subscription.id}`
        }
      ]);

    // إضافة زر العودة
    const backButton = new Discord.ButtonBuilder()
      .setCustomId('panel_back_to_main')
      .setLabel('العودة للقائمة الرئيسية')
      .setStyle(Discord.ButtonStyle.Secondary);

    const actionsRow = new Discord.ActionRowBuilder().addComponents(actionsMenu);
    const backRow = new Discord.ActionRowBuilder().addComponents(backButton);

    // إنشاء رسالة مع معلومات الاشتراك المحدد
    const subEmbed = new Discord.EmbedBuilder()
      .setColor("Green")
      .setTitle(`إدارة الاشتراك: ${subscription.id}`)
      .setDescription(`اختر أحد الإجراءات أدناه للتحكم في هذا الاشتراك.`)
      .addFields([

        {
          name: "عدد البوتات",
          value: `${subscription.bots.length} بوت`,
          inline: true
        },

        {
          name: "معرف السيرفر",
          value: `${subscription.guildID}`,
          inline: true
        },


        {
          name: "الوقت المتبقي",
          value: `${prettyMilliseconds(subscription.endDate - Date.now())}`,
          inline: false
        }
      ])
      .setFooter({ text: "اختر إجراءً من القائمة أعلاه أو اضغط العودة" })
      .setTimestamp();

    await interaction.editReply({ embeds: [subEmbed], components: [actionsRow, backRow] });

  } catch (error) {
    console.error('Error in showSubscriptionManagement:', error);
    if (interaction.deferred) {
      await interaction.editReply({
        content: 'حدث خطأ أثناء عرض إدارة الاشتراك.',
        embeds: [],
        components: []
      }).catch(() => {});
    } else if (!interaction.replied) {
      await interaction.reply({
        content: 'حدث خطأ أثناء عرض إدارة الاشتراك.',
        ephemeral: true
      }).catch(() => {});
    }
  }
}

// دالة معالجة إجراءات اللوحة
async function handlePanelAction(interaction, activeSubscriptions, user) {
  try {
    const actionValue = interaction.values[0];
    console.log('Action value:', actionValue); // للتشخيص

    let action, subId;

    if (actionValue.startsWith('restart_')) {
      action = 'restart';
      subId = actionValue.replace('restart_', '');
    } else if (actionValue.startsWith('transfer_server_')) {
      action = 'transfer';
      subId = actionValue.replace('transfer_server_', '');
    } else if (actionValue.startsWith('transfer_ownership_')) {
      action = 'transfer_ownership';
      subId = actionValue.replace('transfer_ownership_', '');
    } else if (actionValue.startsWith('show_links_')) {
      action = 'show';
      subId = actionValue.replace('show_links_', '');
    } else {
      // fallback للطريقة القديمة
      const parts = actionValue.split('_');
      action = parts[0];
      subId = parts.slice(1).join('_');
    }

    console.log('Parsed action:', action, 'subId:', subId); // للتشخيص
    const subscription = activeSubscriptions.find(sub => sub.id === subId);

    if (!subscription) {
      if (interaction.deferred) {
        await interaction.editReply({ content: "لم يتم العثور على الاشتراك المحدد.", embeds: [], components: [] });
      } else if (!interaction.replied) {
        await interaction.reply({ content: "لم يتم العثور على الاشتراك المحدد.", ephemeral: true });
      }
      return;
    }

    switch (action) {
      case 'restart':
        // إعادة تشغيل الاشتراك
        const confirmButton = new Discord.ButtonBuilder()
          .setCustomId(`panel_confirm_restart_${subscription.id}`)
          .setLabel("تأكيد إعادة التشغيل")
          .setStyle(Discord.ButtonStyle.Success);

        const cancelButton = new Discord.ButtonBuilder()
          .setCustomId('panel_back_to_main')
          .setLabel("إلغاء")
          .setStyle(Discord.ButtonStyle.Danger);

        const confirmRow = new Discord.ActionRowBuilder().addComponents(confirmButton, cancelButton);

        const restartEmbed = new Discord.EmbedBuilder()
          .setColor("Orange")
          .setTitle("تأكيد إعادة التشغيل")
          .setDescription(`هل تريد إعادة تشغيل جميع البوتات في هذا الاشتراك؟`)
          .addFields([
            {
              name: "تفاصيل الاشتراك",
              value: `معرف الاشتراك: ${subscription.id}\nعدد البوتات: ${subscription.bots.length}\nالسيرفر: ${subscription.guildID}`,
              inline: false
            },
            {
              name: "تحذير",
              value: "سيتم إعادة تشغيل جميع البوتات وقد يستغرق ذلك بضع ثوانٍ.",
              inline: false
            }
          ])
          .setFooter({ text: "اضغط تأكيد لإعادة التشغيل أو إلغاء للعودة" });

        await interaction.editReply({ embeds: [restartEmbed], components: [confirmRow] });
        break;

      case 'transfer':
        if (actionValue.includes('server')) {
          // نقل السيرفر - نفس الطريقة المستخدمة في mysub
          const serverEmbed = new Discord.EmbedBuilder()
            .setColor("Blue")
            .setTitle("نقل البوتات إلى سيرفر جديد")
            .setDescription("اضغط على زر إضافة معرف السيرفر لإدخال السيرفر الجديد.")
            .addFields([
              {
                name: "الاشتراك الحالي",
                value: `معرف الاشتراك: ${subscription.id}\nالسيرفر الحالي: ${subscription.guildID}\nعدد البوتات: ${subscription.bots.length}`,
                inline: false
              },
              {
                name: "تعليمات",
                value: "اضغط على الزر أدناه لفتح نموذج إدخال معرف السيرفر الجديد.",
                inline: false
              }
            ])
            .setFooter({ text: "تأكد من أن معرف السيرفر صحيح ومكون من 18-19 رقم" });

          const addServerButton = new Discord.ButtonBuilder()
            .setCustomId(`panel_add_server_id_${subscription.id}`)
            .setLabel('إضافة معرف السيرفر')
            .setStyle(Discord.ButtonStyle.Primary);

          const backButton2 = new Discord.ButtonBuilder()
            .setCustomId('panel_back_to_main')
            .setLabel('إلغاء')
            .setStyle(Discord.ButtonStyle.Secondary);

          const serverRow = new Discord.ActionRowBuilder().addComponents(addServerButton, backButton2);

          await interaction.editReply({ embeds: [serverEmbed], components: [serverRow] });
        }
        break;

      case 'transfer_ownership':
        // نقل ملكية الاشتراك
        const ownershipEmbed = new Discord.EmbedBuilder()
          .setColor("Purple")
          .setTitle("نقل ملكية الاشتراك")
          .setDescription("أدخل معرف المستخدم الجديد لنقل ملكية الاشتراك إليه.")
          .addFields([
            {
              name: "الاشتراك الحالي",
              value: `معرف الاشتراك: ${subscription.id}\nالمالك الحالي: <@${user.id}>\nعدد البوتات: ${subscription.bots.length}`,
              inline: false
            },
            {
              name: "تعليمات",
              value: "اضغط على الزر أدناه لفتح نموذج إدخال معرف المستخدم الجديد.",
              inline: false
            },
            {
              name: "تحذير مهم",
              value: "بعد نقل الملكية، لن تتمكن من الوصول إلى هذا الاشتراك مرة أخرى.",
              inline: false
            }
          ])
          .setFooter({ text: "تأكد من صحة معرف المستخدم قبل التأكيد" });

        const addOwnerButton = new Discord.ButtonBuilder()
          .setCustomId(`panel_add_owner_id_${subscription.id}`)
          .setLabel('إضافة معرف المستخدم')
          .setStyle(Discord.ButtonStyle.Primary);

        const backButton4 = new Discord.ButtonBuilder()
          .setCustomId('panel_back_to_main')
          .setLabel('إلغاء')
          .setStyle(Discord.ButtonStyle.Secondary);

        const ownershipRow = new Discord.ActionRowBuilder().addComponents(addOwnerButton, backButton4);

        await interaction.editReply({ embeds: [ownershipEmbed], components: [ownershipRow] });
        break;

      case 'show':
        // عرض الروابط
        let links = subscription.bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${subscription.guildID}&disable_guild_select=true`);

        const linksEmbed = new Discord.EmbedBuilder()
          .setColor("Blue")
          .setTitle("روابط البوتات")
          .setDescription("تم إرسال روابط البوتات إلى رسائلك الخاصة.")
          .addFields([
            {
              name: "معلومات الاشتراك",
              value: `معرف الاشتراك: ${subscription.id}\nعدد البوتات: ${subscription.bots.length}\nالسيرفر: ${subscription.guildID}`,
              inline: false
            }
          ])
          .setFooter({ text: "تحقق من رسائلك الخاصة للحصول على الروابط" });

        const backButton3 = new Discord.ButtonBuilder()
          .setCustomId('panel_back_to_main')
          .setLabel('العودة للقائمة الرئيسية')
          .setStyle(Discord.ButtonStyle.Secondary);

        const backRow3 = new Discord.ActionRowBuilder().addComponents(backButton3);

        await interaction.editReply({ embeds: [linksEmbed], components: [backRow3] });

        // إرسال الروابط في الخاص
        let n = 10;
        for (let i = 0; i < links.length; i += n) {
          let ee = links.slice(i, i + n);
          await user.send({ content: `${ee.join("\n")}` }).catch(() => 0);
        }
        break;
    }

  } catch (error) {
    console.error('Error in handlePanelAction:', error);
    if (interaction.deferred) {
      await interaction.editReply({
        content: 'حدث خطأ أثناء معالجة الإجراء.',
        embeds: [],
        components: []
      }).catch(() => {});
    } else if (!interaction.replied) {
      await interaction.reply({
        content: 'حدث خطأ أثناء معالجة الإجراء.',
        ephemeral: true
      }).catch(() => {});
    }
  }
}

// دالة بدء عملية الشراء
async function startPurchaseFlow(interaction, user) {
  try {
    // عرض خيارات الاشتراك في نفس الشات (رسالة خاصة للمستخدم فقط)
    const subscriptionTypeEmbed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle("اختيار نوع الاشتراك")
      .setDescription("اختر نوع الاشتراك المطلوب:")
      .addFields([
        {
          name: "اشتراك الموسيقى",
          value: "بوتات موسيقى متخصصة\nدعم جميع منصات الموسيقى\nمدة الاشتراك: 30 يوم",
          inline: false
        },
        {
          name: "تفاصيل الاشتراك",
          value: "الحد الأقصى: 20 بوت\nالمدة: 30 يوم\nالتفعيل: فوري",
          inline: false
        }
      ])
      .setFooter({ text: "اضغط على الزر أدناه للمتابعة" });

    const musicButton = new Discord.ButtonBuilder()
      .setCustomId(`purchase_music_${user.id}`)
      .setLabel('اشتراك الموسيقى')
      .setStyle(Discord.ButtonStyle.Success);

    const cancelButton = new Discord.ButtonBuilder()
      .setCustomId(`purchase_cancel_${user.id}`)
      .setLabel('إلغاء')
      .setStyle(Discord.ButtonStyle.Danger);

    const typeRow = new Discord.ActionRowBuilder().addComponents(musicButton, cancelButton);

    // إرسال رسالة في نفس الشات (خاصة للمستخدم فقط)
    await interaction.reply({
      embeds: [subscriptionTypeEmbed],
      components: [typeRow],
      ephemeral: true
    });

  } catch (error) {
    console.error('Error starting purchase flow:', error);
    await interaction.reply({
      content: "❌ حدث خطأ أثناء بدء عملية الشراء. يرجى المحاولة مرة أخرى.",
      ephemeral: true
    });
  }
}

// دالة معالجة اشتراك الموسيقى باستخدام Modal
async function handleMusicSubscriptionModal(interaction) {
  try {
    // إنشاء Modal لإدخال تفاصيل الاشتراك
    const modal = new Discord.ModalBuilder()
      .setCustomId(`subscription_details_${interaction.user.id}`)
      .setTitle('تفاصيل الاشتراك');

    // حقل معرف السيرفر
    const serverIdInput = new Discord.TextInputBuilder()
      .setCustomId('server_id')
      .setLabel('معرف السيرفر')
      .setPlaceholder('1234567890123456789')
      .setStyle(Discord.TextInputStyle.Short)
      .setMinLength(15)
      .setMaxLength(20)
      .setRequired(true);

    // حقل عدد البوتات
    const botCountInput = new Discord.TextInputBuilder()
      .setCustomId('bot_count')
      .setLabel('عدد البوتات (1-20)')
      .setPlaceholder('5')
      .setStyle(Discord.TextInputStyle.Short)
      .setMinLength(1)
      .setMaxLength(2)
      .setRequired(true);

    // إضافة الحقول إلى صفوف
    const serverRow = new Discord.ActionRowBuilder().addComponents(serverIdInput);
    const botCountRow = new Discord.ActionRowBuilder().addComponents(botCountInput);

    modal.addComponents(serverRow, botCountRow);

    // عرض Modal
    await interaction.showModal(modal);

  } catch (error) {
    console.error('Error showing modal:', error);
    await interaction.reply({
      content: "❌ حدث خطأ أثناء عرض النموذج. يرجى المحاولة مرة أخرى.",
      ephemeral: true
    });
  }
}

// دالة معالجة تفاصيل الاشتراك من Modal
async function handleSubscriptionDetails(interaction) {
  try {
    // الحصول على البيانات من Modal
    const serverId = interaction.fields.getTextInputValue('server_id');
    const botCountStr = interaction.fields.getTextInputValue('bot_count');
    const botCount = parseInt(botCountStr);

    // التحقق من صحة معرف السيرفر
    if (!serverId || isNaN(serverId) || serverId.length < 15) {
      return await interaction.reply({
        content: "معرف السيرفر غير صالح. يجب أن يكون مكون من 15-20 رقم.",
        ephemeral: true
      });
    }

    // التحقق من صحة عدد البوتات
    if (!botCount || isNaN(botCount) || botCount < 1 || botCount > 20) {
      return await interaction.reply({
        content: "عدد البوتات غير صالح. يجب أن يكون بين 1 و 20.",
        ephemeral: true
      });
    }

    // عرض تأكيد الشراء
    await showPurchaseConfirmationModal(interaction, interaction.user, serverId, botCount);

  } catch (error) {
    console.error('Error handling subscription details:', error);
    await interaction.reply({
      content: "حدث خطأ أثناء معالجة البيانات. يرجى المحاولة مرة أخرى.",
      ephemeral: true
    });
  }
}

// دالة عرض تأكيد الشراء (محدثة للـ Modal)
async function showPurchaseConfirmationModal(interaction, user, guildId, botCount) {
  try {
    const confirmEmbed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle("تأكيد طلب الشراء")
      .setDescription("مراجعة تفاصيل الطلب:")
      .addFields([
        {
          name: "المشتري",
          value: `${user.username} (${user.id})`,
          inline: true
        },
        {
          name: "نوع الاشتراك",
          value: "بوت موسيقى",
          inline: true
        },
        {
          name: "عدد البوتات",
          value: `${botCount} بوت`,
          inline: true
        },
        {
          name: "معرف السيرفر",
          value: `\`${guildId}\``,
          inline: true
        },
        {
          name: "مدة الاشتراك",
          value: "30 يوم",
          inline: true
        },
        {
          name: "تاريخ البدء",
          value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
          inline: true
        },
        {
          name: "تاريخ الانتهاء",
          value: `<t:${Math.floor((Date.now() + (30 * 24 * 60 * 60 * 1000)) / 1000)}:F>`,
          inline: false
        },
        {
          name: "ملاحظة مهمة",
          value: "بعد التأكيد، سيتم إنشاء الاشتراك وتشغيل البوتات تلقائياً. تأكد من صحة جميع البيانات.",
          inline: false
        }
      ])
      .setFooter({ text: "💡 اضغط على تأكيد الشراء لإتمام العملية" });

    const confirmButton = new Discord.ButtonBuilder()
      .setCustomId(`confirm_purchase_modal_${user.id}_${guildId}_${botCount}`)
      .setLabel('تأكيد الشراء')
      .setStyle(Discord.ButtonStyle.Success);

    const cancelButton = new Discord.ButtonBuilder()
      .setCustomId(`cancel_purchase_modal_${user.id}`)
      .setLabel('إلغاء')
      .setStyle(Discord.ButtonStyle.Danger);

    const confirmRow = new Discord.ActionRowBuilder().addComponents(confirmButton, cancelButton);

    await interaction.update({
      embeds: [confirmEmbed],
      components: [confirmRow]
    });

  } catch (error) {
    console.error('Error in purchase confirmation modal:', error);
    await interaction.update({
      content: "حدث خطأ أثناء عرض تأكيد الشراء.",
      embeds: [],
      components: []
    });
  }
}

// دالة معالجة الشراء النهائية من Modal
async function processPurchaseModal(interaction, user, guildId, botCount) {
  try {
    // عرض رسالة المعالجة
    const processingEmbed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle("🔄 جاري معالجة الشراء...")
      .setDescription("يرجى الانتظار بينما نقوم بإنشاء اشتراكك وتشغيل البوتات.")
      .setFooter({ text: "هذا قد يستغرق بضع ثوانٍ..." });

    await interaction.update({ embeds: [processingEmbed], components: [] });

    // التحقق من توفر التوكنات
    const fs = require('fs');
    let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
    let tokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

    if (!tokens || tokens.length < botCount) {
      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ غير متوفر حالياً")
        .setDescription(`عذراً، لا يتوفر لدينا ${botCount} بوت في الوقت الحالي. المتوفر: ${tokens.length} بوت`)
        .setFooter({ text: "يرجى المحاولة مرة أخرى لاحقاً أو اختيار عدد أقل" });

      await interaction.editReply({ embeds: [errorEmbed], components: [] });
      return;
    }

    // إنشاء الاشتراك
    const startDate = Date.now();
    const endDate = Date.now() + (30 * 24 * 60 * 60 * 1000); // 30 يوم
    const subId = Date.now().toString(16);

    // أخذ التوكنات المطلوبة
    let bots_tokens = tokens.slice(0, botCount).map(t => ({ ...t, type: "music" }));

    // تحديث قائمة التوكنات
    tokens = tokens.slice(botCount);
    tokensData.tokens = tokens;
    fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

    // تحديث قاعدة البيانات في الذاكرة
    await tokens_db.set("tokens", tokens);

    const sub_data = {
      id: subId,
      bots: bots_tokens,
      startDate,
      endDate,
      guildID: guildId,
      type: "music",
      ended: false
    };

    // إضافة الاشتراك إلى قاعدة البيانات
    await db.push(user.id, sub_data);

    // تحديث ملف الاشتراكات مباشرة
    try {
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));
      if (!subsData[user.id]) {
        subsData[user.id] = [];
      }
      subsData[user.id].push(sub_data);
      fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
    } catch (error) {
      console.error(`Error updating subscriptions file:`, error);
    }

    // إعطاء رتبة الموسيقى للمستخدم
    try {
      for (const guild of client.guilds.cache.values()) {
        try {
          const member = await guild.members.fetch(user.id).catch(() => null);
          if (member) {
            const musicRoleId = "1118261695261982773";
            const musicRole = guild.roles.cache.get(musicRoleId);

            if (musicRole && !member.roles.cache.has(musicRoleId)) {
              await member.roles.add(musicRole);
              console.log(`Added music subscription role to user ${user.id} via panel modal purchase`);
              break;
            }
          }
        } catch (guildError) {
          console.error(`Error processing guild ${guild.id} for role:`, guildError);
        }
      }
    } catch (error) {
      console.error("Error adding music role to user via panel modal:", error);
    }

    // تشغيل الاشتراك
    runSub(user.id, sub_data);

    // عرض رسالة النجاح
    const successEmbed = new Discord.EmbedBuilder()
      .setColor("Green")
      .setTitle("🎉 تم إنشاء الاشتراك بنجاح!")
      .setDescription("تهانينا! تم إنشاء اشتراكك وتشغيل البوتات بنجاح.")
      .addFields([
        {
          name: "معرف الاشتراك",
          value: `\`${subId}\``,
          inline: true
        },
        {
          name: "عدد البوتات",
          value: `${botCount} بوت`,
          inline: true
        },
        {
          name: "مدة الاشتراك",
          value: "30 يوم",
          inline: true
        },
        {
          name: "ينتهي في",
          value: `<t:${Math.floor(endDate / 1000)}:R>`,
          inline: false
        },
        {
          name: "🔗 الخطوات التالية",
          value: `• ستحصل على روابط البوتات في رسائلك الخاصة\n• اضغط على زر اشتراكاتي لإدارة الاشتراك`,
          inline: false
        }
      ])
      .setFooter({ text: "شكراً لك على استخدام خدماتنا!" })
      .setTimestamp();

    await interaction.editReply({ embeds: [successEmbed], components: [] });

    // إرسال الروابط في الرسائل الخاصة
    try {
      let links = sub_data.bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${guildId}&disable_guild_select=true`);
      let n = 10;
      for (let i = 0; i < links.length; i += n) {
        let ee = links.slice(i, i + n);
        await user.send({ content: `🔗 **روابط البوتات:**\n${ee.join("\n")}` }).catch(() => 0);
      }
    } catch (error) {
      console.error('Error sending links to user:', error);
    }

    // إرسال لوج للمسؤولين
    let log_ch = client.channels.cache.get(config.log_channel);
    if (log_ch) {
      let embed = new Discord.EmbedBuilder()
        .setColor("Green")
        .setTitle("🛒 شراء اشتراك جديد عبر Berlin Smart System")
        .addFields([
          {
            name: `معرف الاشتراك:`,
            value: `${sub_data.id}`,
          },
          {
            name: `المشتري:`,
            value: `<@${user.id}> (ID: ${user.id})`
          },
          {
            name: `عدد البوتات:`,
            value: `${sub_data.bots.length} بوت`
          },
          {
            name: `نوع الاشتراك:`,
            value: "موسيقى"
          },
          {
            name: `تاريخ البدء:`,
            value: `<t:${Math.floor(sub_data.startDate / 1000)}:R>`
          },
          {
            name: `مدة الاشتراك:`,
            value: "30 يوم"
          },
          {
            name: "معرف السيرفر:",
            value: `${sub_data.guildID}`
          }
        ])
        .setTimestamp();
      log_ch.send({ embeds: [embed] });
    }

    console.log(`Panel modal purchase completed: User ${user.id} bought ${botCount} music bots for guild ${guildId}`);

  } catch (error) {
    console.error('Error processing modal purchase:', error);

    const errorEmbed = new Discord.EmbedBuilder()
      .setColor("Red")
      .setTitle("❌ خطأ في معالجة الشراء")
      .setDescription("حدث خطأ أثناء معالجة طلب الشراء. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.")
      .setFooter({ text: "نعتذر عن الإزعاج" });

    await interaction.editReply({ embeds: [errorEmbed], components: [] });
  }
}

// دالة معالجة عدد البوتات
async function handleBotCount(message, user, guildId) {
  try {
    // طلب عدد البوتات
    const countEmbed = new Discord.EmbedBuilder()
      .setColor("Purple")
      .setTitle("اختيار عدد البوتات")
      .setDescription("يرجى اختيار عدد بوتات الموسيقى التي تريدها:")
      .addFields([
        {
          name: "الحدود المسموحة",
          value: "• الحد الأدنى: 1 بوت\n• الحد الأقصى: 20 بوت",
          inline: false
        },
        {
          name: "التكلفة",
          value: "سيتم تحديد التكلفة بناءً على عدد البوتات المختارة",
          inline: false
        },
        {
          name: "المدة",
          value: "30 يوم تلقائياً لجميع الاشتراكات",
          inline: false
        }
      ])
      .setFooter({ text: "💡 اكتب عدد البوتات المطلوب (1-20)" });

    await message.edit({ embeds: [countEmbed], components: [] });

    // انتظار رد المستخدم
    const countFilter = m => m.author.id === user.id;
    const countCollector = message.channel.createMessageCollector({
      filter: countFilter,
      time: 120000, // دقيقتان
      max: 1
    });

    countCollector.on('collect', async countMessage => {
      const botCount = parseInt(countMessage.content.trim());

      // التحقق من صحة عدد البوتات
      if (!botCount || isNaN(botCount) || botCount < 1 || botCount > 20) {
        const errorEmbed = new Discord.EmbedBuilder()
          .setColor("Red")
          .setTitle("❌ عدد بوتات غير صالح")
          .setDescription("يرجى إدخال عدد صحيح بين 1 و 20.")
          .setFooter({ text: "يرجى المحاولة مرة أخرى برقم صحيح" });

        await message.edit({ embeds: [errorEmbed], components: [] });
        await countMessage.delete().catch(() => {});
        return;
      }

      await countMessage.delete().catch(() => {});
      await showPurchaseConfirmation(message, user, guildId, botCount);
    });

    countCollector.on('end', collected => {
      if (collected.size === 0) {
        const timeoutEmbed = new Discord.EmbedBuilder()
          .setColor("Orange")
          .setTitle("⏰ انتهت المهلة الزمنية")
          .setDescription("انتهت مهلة إدخال عدد البوتات.")
          .setFooter({ text: "يرجى بدء عملية الشراء مرة أخرى" });

        message.edit({ embeds: [timeoutEmbed], components: [] }).catch(() => {});
      }
    });

  } catch (error) {
    console.error('Error in bot count handling:', error);
  }
}

// دالة عرض تأكيد الشراء
async function showPurchaseConfirmation(message, user, guildId, botCount) {
  try {
    const confirmEmbed = new Discord.EmbedBuilder()
      .setColor("Gold")
      .setTitle("✅ تأكيد طلب الشراء")
      .setDescription("يرجى مراجعة تفاصيل طلبك قبل التأكيد:")
      .addFields([
        {
          name: "👤 المشتري",
          value: `${user.username} (${user.id})`,
          inline: true
        },
        {
          name: "🎵 نوع الاشتراك",
          value: "بوتات موسيقى",
          inline: true
        },
        {
          name: "🤖 عدد البوتات",
          value: `${botCount} بوت`,
          inline: true
        },
        {
          name: "🏠 معرف السيرفر",
          value: `\`${guildId}\``,
          inline: true
        },
        {
          name: "⏰ مدة الاشتراك",
          value: "30 يوم",
          inline: true
        },
        {
          name: "📅 تاريخ البدء",
          value: `<t:${Math.floor(Date.now() / 1000)}:F>`,
          inline: true
        },
        {
          name: "📅 تاريخ الانتهاء",
          value: `<t:${Math.floor((Date.now() + (30 * 24 * 60 * 60 * 1000)) / 1000)}:F>`,
          inline: false
        },
        {
          name: "⚠️ ملاحظة مهمة",
          value: "بعد التأكيد، سيتم إنشاء الاشتراك وتشغيل البوتات تلقائياً. تأكد من صحة جميع البيانات.",
          inline: false
        }
      ])
      .setFooter({ text: "💡 اضغط على تأكيد الشراء لإتمام العملية" });

    const confirmButton = new Discord.ButtonBuilder()
      .setCustomId(`confirm_purchase_${guildId}_${botCount}`)
      .setLabel('✅ تأكيد الشراء')
      .setStyle(Discord.ButtonStyle.Success);

    const cancelButton = new Discord.ButtonBuilder()
      .setCustomId('cancel_purchase')
      .setLabel('❌ إلغاء')
      .setStyle(Discord.ButtonStyle.Danger);

    const confirmRow = new Discord.ActionRowBuilder().addComponents(confirmButton, cancelButton);

    await message.edit({ embeds: [confirmEmbed], components: [confirmRow] });

    // إنشاء مجمع للتفاعلات
    const confirmCollector = message.createMessageComponentCollector({
      filter: i => i.user.id === user.id,
      time: 300000 // 5 دقائق
    });

    confirmCollector.on('collect', async confirmInteraction => {
      try {
        if (confirmInteraction.customId.startsWith('confirm_purchase_')) {
          await confirmInteraction.deferUpdate();
          const parts = confirmInteraction.customId.split('_');
          const finalGuildId = parts[2];
          const finalBotCount = parseInt(parts[3]);

          await processPurchase(message, user, finalGuildId, finalBotCount);
        } else if (confirmInteraction.customId === 'cancel_purchase') {
          const cancelEmbed = new Discord.EmbedBuilder()
            .setColor("Red")
            .setTitle("❌ تم إلغاء الشراء")
            .setDescription("تم إلغاء عملية الشراء بنجاح.")
            .setFooter({ text: "يمكنك بدء عملية شراء جديدة في أي وقت" });

          await confirmInteraction.update({ embeds: [cancelEmbed], components: [] });
        }
      } catch (error) {
        console.error('Error in purchase confirmation:', error);
      }
    });

    confirmCollector.on('end', collected => {
      if (collected.size === 0) {
        const timeoutEmbed = new Discord.EmbedBuilder()
          .setColor("Orange")
          .setTitle("⏰ انتهت المهلة الزمنية")
          .setDescription("انتهت مهلة تأكيد الشراء.")
          .setFooter({ text: "يرجى بدء عملية الشراء مرة أخرى" });

        message.edit({ embeds: [timeoutEmbed], components: [] }).catch(() => {});
      }
    });

  } catch (error) {
    console.error('Error in purchase confirmation:', error);
  }
}

// دالة معالجة الشراء النهائية
async function processPurchase(message, user, guildId, botCount) {
  try {
    // عرض رسالة المعالجة
    const processingEmbed = new Discord.EmbedBuilder()
      .setColor("Blue")
      .setTitle("🔄 جاري معالجة الشراء...")
      .setDescription("يرجى الانتظار بينما نقوم بإنشاء اشتراكك وتشغيل البوتات.")
      .setFooter({ text: "هذا قد يستغرق بضع ثوانٍ..." });

    await message.edit({ embeds: [processingEmbed], components: [] });

    // التحقق من توفر التوكنات
    const fs = require('fs');
    let tokensData = JSON.parse(fs.readFileSync('./databases/tokens.json', 'utf8'));
    let tokens = tokensData && tokensData.tokens && Array.isArray(tokensData.tokens) ? tokensData.tokens : [];

    if (!tokens || tokens.length < botCount) {
      const errorEmbed = new Discord.EmbedBuilder()
        .setColor("Red")
        .setTitle("❌ غير متوفر حالياً")
        .setDescription(`عذراً، لا يتوفر لدينا ${botCount} بوت في الوقت الحالي. المتوفر: ${tokens.length} بوت`)
        .setFooter({ text: "يرجى المحاولة مرة أخرى لاحقاً أو اختيار عدد أقل" });

      await message.edit({ embeds: [errorEmbed], components: [] });
      return;
    }

    // إنشاء الاشتراك
    const startDate = Date.now();
    const endDate = Date.now() + (30 * 24 * 60 * 60 * 1000); // 30 يوم
    const subId = Date.now().toString(16);

    // أخذ التوكنات المطلوبة
    let bots_tokens = tokens.slice(0, botCount).map(t => ({ ...t, type: "music" }));

    // تحديث قائمة التوكنات
    tokens = tokens.slice(botCount);
    tokensData.tokens = tokens;
    fs.writeFileSync('./databases/tokens.json', JSON.stringify(tokensData, null, 2));

    // تحديث قاعدة البيانات في الذاكرة
    await tokens_db.set("tokens", tokens);

    const sub_data = {
      id: subId,
      bots: bots_tokens,
      startDate,
      endDate,
      guildID: guildId,
      type: "music",
      ended: false
    };

    // إضافة الاشتراك إلى قاعدة البيانات
    await db.push(user.id, sub_data);

    // تحديث ملف الاشتراكات مباشرة
    try {
      const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));
      if (!subsData[user.id]) {
        subsData[user.id] = [];
      }
      subsData[user.id].push(sub_data);
      fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
    } catch (error) {
      console.error(`Error updating subscriptions file:`, error);
    }

    // إعطاء رتبة الموسيقى للمستخدم
    try {
      for (const guild of client.guilds.cache.values()) {
        try {
          const member = await guild.members.fetch(user.id).catch(() => null);
          if (member) {
            const musicRoleId = "1118261695261982773";
            const musicRole = guild.roles.cache.get(musicRoleId);

            if (musicRole && !member.roles.cache.has(musicRoleId)) {
              await member.roles.add(musicRole);
              console.log(`Added music subscription role to user ${user.id} via panel purchase`);
              break;
            }
          }
        } catch (guildError) {
          console.error(`Error processing guild ${guild.id} for role:`, guildError);
        }
      }
    } catch (error) {
      console.error("Error adding music role to user via panel:", error);
    }

    // تشغيل الاشتراك
    runSub(user.id, sub_data);

    // عرض رسالة النجاح
    const successEmbed = new Discord.EmbedBuilder()
      .setColor("Green")
      .setTitle("🎉 تم إنشاء الاشتراك بنجاح!")
      .setDescription("تهانينا! تم إنشاء اشتراكك وتشغيل البوتات بنجاح.")
      .addFields([
        {
          name: "🆔 معرف الاشتراك",
          value: `\`${subId}\``,
          inline: true
        },
        {
          name: "🤖 عدد البوتات",
          value: `${botCount} بوت`,
          inline: true
        },
        {
          name: "⏰ مدة الاشتراك",
          value: "30 يوم",
          inline: true
        },
        {
          name: "📅 ينتهي في",
          value: `<t:${Math.floor(endDate / 1000)}:R>`,
          inline: false
        },
        {
          name: "🔗 الخطوات التالية",
          value: `• ستحصل على روابط البوتات قريباً\n• استخدم \`${prefix}mylinks ${subId}\` للحصول على الروابط\n• استخدم \`${prefix}mysub\` لإدارة اشتراكاتك`,
          inline: false
        }
      ])
      .setFooter({ text: "شكراً لك على استخدام خدماتنا!" })
      .setTimestamp();

    await message.edit({ embeds: [successEmbed], components: [] });

    // إرسال الروابط
    let links = sub_data.bots.map((b, i) => `${i + 1}. https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${guildId}&disable_guild_select=true`);
    let n = 10;
    for (let i = 0; i < links.length; i += n) {
      let ee = links.slice(i, i + n);
      await user.send({ content: `🔗 **روابط البوتات:**\n${ee.join("\n")}` }).catch(() => 0);
    }

    // إرسال لوج للمسؤولين
    let log_ch = client.channels.cache.get(config.log_channel);
    if (log_ch) {
      let embed = new EmbedBuilder()
        .setColor("Green")
        .setTitle("🛒 شراء اشتراك جديد عبر Panel")
        .addFields([
          {
            name: `معرف الاشتراك:`,
            value: `${sub_data.id}`,
          },
          {
            name: `المشتري:`,
            value: `<@${user.id}> (ID: ${user.id})`
          },
          {
            name: `عدد البوتات:`,
            value: `${sub_data.bots.length} بوت`
          },
          {
            name: `نوع الاشتراك:`,
            value: "موسيقى"
          },
          {
            name: `تاريخ البدء:`,
            value: `<t:${Math.floor(sub_data.startDate / 1000)}:R>`
          },
          {
            name: `مدة الاشتراك:`,
            value: "30 يوم"
          },
          {
            name: "معرف السيرفر:",
            value: `${sub_data.guildID}`
          }
        ])
        .setTimestamp();
      log_ch.send({ embeds: [embed] });
    }

    console.log(`Panel purchase completed: User ${user.id} bought ${botCount} music bots for guild ${guildId}`);

  } catch (error) {
    console.error('Error processing purchase:', error);

    const errorEmbed = new Discord.EmbedBuilder()
      .setColor("Red")
      .setTitle("❌ خطأ في معالجة الشراء")
      .setDescription("حدث خطأ أثناء معالجة طلب الشراء. يرجى المحاولة مرة أخرى أو التواصل مع الدعم الفني.")
      .setFooter({ text: "نعتذر عن الإزعاج" });

    await message.edit({ embeds: [errorEmbed], components: [] });
  }
}

async function getId(token) {
  return new Promise(async solve => {
    try {
      fetch("https://discord.com/api/users/@me", {
        method: "GET",
        headers: {
          Authorization: `Bot ${token}`
        }
      }).then(async data => {
        try {
          data = await data.json();
        } catch {
          data = null;
        }
        if (!data || !data.id) return solve(null);
        solve(data.id);
      }).catch(() => solve(null));
    } catch {
      solve(null);
    }
  });
}

async function restartSub(subscription) {
  if (subscription.type === "music") {
    pm2.restart(subscription.id);
    return;
  } else {
    for (let bot of subscription.bots) {
      await sleep(200);
      pm2.restart(bot.botId);
    }
    return;
  }
}

async function endSub(userId, sub) {
  if (sub.type === "music") {
    pm2.delete(sub.id, (err) => {
      if (err) {
        let channel = client.channels.cache.get(config.err_channel);
        if (channel) {
          channel.send({ content: `**😢 I can't delete this process \`${sub.id}\`**` }).catch(() => 0);
        }
      }
    });
  } else {
    for (let bot of sub.bots) {
      pm2.delete(bot.botId, (err) => {
        if (err) {
          let channel = client.channels.cache.get(config.err_channel);
          if (channel) {
            channel.send({ content: `😢 I can't delete process for this bot \`${bot.botId}\`` }).catch(() => 0);
          }
        }
      });
    }
  }
  let channel = client.channels.cache.get(config.log_channel);
  let embed = new Discord.EmbedBuilder()
    .setTitle("Subscription End")
    .setColor("Red")
    .addFields([
      {
        name: `Subscription  ID:`,
        value: `${sub.id}`,
      },
      {
        name: `Author:`,
        value: `<@${userId}> (ID: ${userId})`
      },
      {
        name: `Bots count:`,
        value: `${sub.bots.length} bot`
      },
      {
        name: `Subscription type:`,
        value: `${sub.bots.find(e => e.type?.toLowerCase() == "music") ? "music" : sub.bots.map(t => t.type).join(", ")}`
      },
      {
        name: `Subscription Start At:`,
        value: `<t:${Math.floor(sub.startDate / 1000)}:R>`
      },
      {
        name: `Subscription duration:`,
        value: `${ms(Math.floor(sub.endDate - sub.startDate))}`
      },
      {
        name: "Guild ID:",
        value: `${sub.guildID}`
      }
    ]);
  channel?.send({ embeds: [embed] }).catch(() => 0);
  return;
}

async function runSub(userId, subscription) {
  try {
    console.log(`Running subscription for user ${userId}, subID: ${subscription.id}`);

    // التحقق من صلاحية الاشتراك
    if (subscription.ended === true) {
      console.log(`Subscription ${subscription.id} for user ${userId} is marked as ended`);
      return;
    }

    if (subscription.endDate <= Date.now()) {
      console.log(`Subscription ${subscription.id} for user ${userId} is expired (endDate: ${new Date(subscription.endDate)})`);
      return;
    }

    console.log(`Subscription ${subscription.id} for user ${userId} is active (endDate: ${new Date(subscription.endDate)}, ended: ${subscription.ended})`);

    // التحقق من وجود البوتات في الاشتراك
    if (!subscription.bots || !Array.isArray(subscription.bots) || subscription.bots.length === 0) {
      if (!subscription.type) {
        console.log(`Subscription ${subscription.id} for user ${userId} has no bots and no type`);
        return;
      }
    }

    // جدولة إشعار منتصف المدة
    let middle_time = subscription.startDate + ((subscription.endDate - subscription.startDate) / 2);
    if (middle_time > Date.now()) {
      console.log(`Scheduling half-time reminder for subscription ${subscription.id} at ${new Date(middle_time)}`);
      setTime(async () => {
        try {
          let data = await db.get(userId);
          if (!data) return;
          let sub = data.find(s => s.id == subscription.id);
          if (!sub || !sub.bots || sub.endDate != subscription.endDate || sub.ended) return;
          let user = await client.users.fetch(userId).catch(fetchErr => {
            console.error(`Error fetching user ${userId} for half-time reminder:`, fetchErr);
            return null;
          });
          if (user) {
            let image = await alert_image("half_time", user.displayAvatarURL({ dynamic: true, format: "png" }).split(".").slice(0, user.displayAvatarURL({ dynamic: true, format: "png" }).split(".").length - 1).join(".") + ".png?size=1024");
            user.send({ content: `Sub Id: \`${sub.id}\``, files: [{ attachment: image, name: "alert.png" }] }).catch(dmErr => {
              console.error(`Error sending half-time reminder to user ${userId}:`, dmErr);
            });
          }
        } catch (halfTimeError) {
          console.error(`Error in half-time reminder for subscription ${subscription.id}:`, halfTimeError);
        }
      }, middle_time);
    }

    // جدولة إشعار قبل انتهاء الاشتراك بيومين
    if (subscription.endDate - Date.now() > ms("2d")) {
      console.log(`Scheduling 2-day reminder for subscription ${subscription.id} at ${new Date(subscription.endDate - ms("2d"))}`);
      setTime(async () => {
        try {
          let data = await db.get(userId);
          if (!data) return;
          let sub = data.find(s => s.id == subscription.id);
          if (!sub || !sub.bots || sub.endDate != subscription.endDate || sub.ended) return;
          let user = await client.users.fetch(userId).catch(fetchErr => {
            console.error(`Error fetching user ${userId} for 2-day reminder:`, fetchErr);
            return null;
          });
          if (user) {
            let image = await alert_image("2_days", user.displayAvatarURL({ dynamic: true, format: "png" }).split(".").slice(0, user.displayAvatarURL({ dynamic: true, format: "png" }).split(".").length - 1).join(".") + ".png?size=1024");
            user.send({ content: `Sub Id: \`${sub.id}\``, files: [{ attachment: image, name: "alert.png" }] }).catch(dmErr => {
              console.error(`Error sending 2-day reminder to user ${userId}:`, dmErr);
            });
          }
        } catch (twoDayError) {
          console.error(`Error in 2-day reminder for subscription ${subscription.id}:`, twoDayError);
        }
      }, subscription.endDate - ms("2d"));
    }

    // جدولة إنهاء الاشتراك
    console.log(`Scheduling end of subscription ${subscription.id} at ${new Date(subscription.endDate)}`);
    setTime(async () => {
      try {
        let data = await db.get(userId);
        if (!data) return;
        let sub_index = data.findIndex(s => s.id == subscription.id);
        let sub = data.find(s => s.id == subscription.id);
        if (!sub || !sub.bots || sub.endDate != subscription.endDate || sub_index == -1 || sub.ended) return;
        data[sub_index].ended = true;
        await db.set(userId, data);
        await endSub(userId, sub);
        let user = await client.users.fetch(userId).catch(fetchErr => {
          console.error(`Error fetching user ${userId} for end notification:`, fetchErr);
          return null;
        });
        if (user) {
          user.send({ content: `**Your Subscription ended.\nIf you want to renew the subscription, you can contact technical support.**\nSubscription Id: \`${sub.id}\`` }).catch(dmErr => {
            console.error(`Error sending end notification to user ${userId}:`, dmErr);
          });
        }
      } catch (endError) {
        console.error(`Error ending subscription ${subscription.id}:`, endError);
      }
    }, subscription.endDate);

    // تشغيل البوتات
    return new Promise(async (resolve) => {
      try {
        if (subscription.type === "music") {
          console.log(`Starting music bot for subscription ${subscription.id}`);
          pm2.describe(subscription.id, (descErr, descData) => {
            if (descErr || !descData || descData.length < 1) {
              pm2.start({
                script: `projects/music/index.js`,
                name: subscription.id,
                env: {
                  subscription_id: subscription.id,
                  owner_id: userId,
                  guild_id: subscription.guildID
                }
              }, (startErr) => {
                if (startErr) {
                  console.error(`Error starting music bot for subscription ${subscription.id}:`, startErr);
                } else {
                  console.log(`[${subscription.id}] - Music bot started successfully`);
                }
              });
            } else {
              console.log(`Music bot for subscription ${subscription.id} is already running`);
            }
          });
        } else if (subscription.bots && Array.isArray(subscription.bots)) {
          for (let bot of subscription.bots) {
            if (!bot.type || !bot.botId || !bot.botToken) {
              console.log(`Invalid bot data in subscription ${subscription.id}:`, bot);
              continue;
            }

            // التحقق من وجود مجلد المشروع
            if (!fs.existsSync(`./projects/${bot.type}`)) {
              console.log(`Project path ./projects/${bot.type} does not exist for bot type ${bot.type}`);
              continue;
            }

            console.log(`Starting bot ${bot.botId} of type ${bot.type} for subscription ${subscription.id}`);

            pm2.describe(bot.botId, (descErr, descData) => {
              if (descErr || !descData || descData.length < 1) {
                pm2.start({
                  script: `projects/${bot.type}/index.js`,
                  name: bot.botId,
                  env: {
                    token: bot.botToken,
                    subscription_id: subscription.id,
                    owner_id: userId,
                    guild_id: subscription.guildID
                  }
                }, (startErr) => {
                  if (startErr) {
                    console.error(`Error starting bot ${bot.botId} of type ${bot.type}:`, startErr);
                  } else {
                    console.log(`[${bot.botId}] - Bot started successfully`);
                  }
                });
              } else {
                console.log(`Bot ${bot.botId} is already running`);
              }
            });

            await sleep(pm2_cooldown);
          }
        } else {
          console.log(`No bots found in subscription ${subscription.id}`);
        }

        await sleep(pm2_cooldown);
        resolve(subscription);
      } catch (error) {
        console.error(`Error starting bots for subscription ${subscription.id}:`, error);
        resolve(subscription);
      }
    });
  } catch (error) {
    console.error(`Error in runSub for user ${userId}, subscription ${subscription.id}:`, error);
    return null;
  }
}

async function sleep(ms) {
  return new Promise((r) => setTimeout(() => r(ms), ms));
}

function setTime(func, time) {
  let ti = time - Date.now();
  let max = 24 * 24 * 60 * 60000;
  if (ti > max) {
    setTimeout(() => setTime(func, time), max);
  } else {
    setTimeout(func, ti);
  }
}

async function alert_image(path, avatar) {
  let background = await canvas.loadImage(path + ".png");
  avatar = await canvas.loadImage(avatar);
  let Canvas = canvas.createCanvas(1000, 562);
  let ctx = Canvas.getContext("2d");
  ctx.drawImage(background, 0, 0, Canvas.width, Canvas.height);
  ctx.beginPath();
  ctx.arc(254, 206, 108, 0, Math.PI * 2, true);
  ctx.closePath();
  ctx.clip();
  ctx.drawImage(avatar, 146, 98, 216, 216);
  return Canvas.toBuffer();
}

// إضافة مستمع للأزرار والنماذج
client.on('interactionCreate', async interaction => {
  // معالجة أزرار إدارة الاشتراك
  if (interaction.isButton()) {
    const buttonId = interaction.customId;

    // معالجة زر إعادة تشغيل الاشتراك
    if (buttonId.startsWith('restart_sub_')) {
      await interaction.deferUpdate().catch(() => { });

      // استخراج معرف الاشتراك من معرف الزر
      const subId = buttonId.replace('restart_sub_', '');

      // الحصول على بيانات المستخدم
      const userData = await db.get(interaction.user.id);
      if (!userData) {
        await interaction.followUp({ content: "❌ لم يتم العثور على بيانات المستخدم", ephemeral: true }).catch(() => { });
        return;
      }

      // البحث عن الاشتراك
      const subscription = userData.find(s => s.id === subId);
      if (!subscription) {
        await interaction.followUp({ content: "❌ لم يتم العثور على الاشتراك", ephemeral: true }).catch(() => { });
        return;
      }

      try {
        // تحديث رسالة التأكيد
        const processingEmbed = new Discord.EmbedBuilder()
          .setColor("Blue")
          .setTitle("🔄 جاري إعادة تشغيل البوتات...")
          .setDescription("يرجى الانتظار، قد تستغرق هذه العملية بضع ثوان.");

        await interaction.editReply({
          embeds: [processingEmbed],
          components: []
        }).catch(() => { });

        // إعادة تشغيل الاشتراك
        await restartSub(subscription);

        // تحديث رسالة التأكيد بعد الانتهاء
        const successEmbed = new Discord.EmbedBuilder()
          .setColor("Green")
          .setTitle("✅ تمت إعادة تشغيل البوتات بنجاح")
          .setDescription(`تمت إعادة تشغيل جميع البوتات (${subscription.bots.length}) في الاشتراك \`${subscription.id}\` بنجاح.`)
          .setTimestamp();

        await interaction.editReply({
          embeds: [successEmbed],
          components: []
        }).catch(() => { });
      } catch (error) {
        console.error("Error restarting subscription:", error);

        // تحديث رسالة التأكيد بخطأ
        const errorEmbed = new Discord.EmbedBuilder()
          .setColor("Red")
          .setTitle("❌ حدث خطأ")
          .setDescription(`حدث خطأ أثناء محاولة إعادة تشغيل الاشتراك: ${error.message}`)
          .setTimestamp();

        await interaction.editReply({
          embeds: [errorEmbed],
          components: []
        }).catch(() => { });
      }
    }
    // معالجة زر نقل البوتات إلى سيرفر جديد
    else if (buttonId.startsWith('server_transfer_')) {
      // عرض نموذج إدخال معرف السيرفر الجديد
      const subId = buttonId.replace('server_transfer_', '');
      const modalId = `server_transfer_modal_${subId}`;
      const modal = new Discord.ModalBuilder()
        .setCustomId(modalId)
        .setTitle('نقل البوتات إلى سيرفر جديد');

      // إضافة حقل إدخال معرف السيرفر
      const guildIdInput = new Discord.TextInputBuilder()
        .setCustomId('new_guild_id')
        .setLabel('أدخل معرف السيرفر الجديد')
        .setPlaceholder('مثال: 123456789012345678')
        .setStyle(Discord.TextInputStyle.Short)
        .setMinLength(17)
        .setMaxLength(19)
        .setRequired(true);

      // إضافة الحقل إلى صف
      const firstActionRow = new Discord.ActionRowBuilder().addComponents(guildIdInput);

      // إضافة الصف إلى النموذج
      modal.addComponents(firstActionRow);

      // عرض النموذج للمستخدم
      await interaction.showModal(modal).catch(error => {
        console.error("Error showing modal:", error);
      });
    }
    // معالجة زر نقل ملكية الاشتراك
    else if (buttonId.startsWith('ownership_transfer_')) {
      // عرض نموذج إدخال معرف المستخدم الجديد
      const subId = buttonId.replace('ownership_transfer_', '');
      const modalId = `ownership_transfer_modal_${subId}`;
      const modal = new Discord.ModalBuilder()
        .setCustomId(modalId)
        .setTitle('نقل ملكية الاشتراك');

      // إضافة حقل إدخال معرف المستخدم
      const userIdInput = new Discord.TextInputBuilder()
        .setCustomId('new_owner_id')
        .setLabel('أدخل معرف المستخدم الجديد')
        .setPlaceholder('مثال: 123456789012345678')
        .setStyle(Discord.TextInputStyle.Short)
        .setMinLength(17)
        .setMaxLength(19)
        .setRequired(true);

      // إضافة الحقل إلى صف
      const firstActionRow = new Discord.ActionRowBuilder().addComponents(userIdInput);

      // إضافة الصف إلى النموذج
      modal.addComponents(firstActionRow);

      // عرض النموذج للمستخدم
      await interaction.showModal(modal).catch(error => {
        console.error("Error showing modal:", error);
      });
    }
  }
  // معالجة النماذج
  else if (interaction.isModalSubmit()) {
    const modalId = interaction.customId;

    // معالجة نموذج نقل البوتات إلى سيرفر جديد
    if (modalId.startsWith('server_transfer_modal_')) {
      await interaction.deferReply({ ephemeral: true });

      // استخراج معرف الاشتراك من معرف النموذج
      const subId = modalId.replace('server_transfer_modal_', '');

      // الحصول على معرف السيرفر الجديد من النموذج
      const newGuildId = interaction.fields.getTextInputValue('new_guild_id');

      // التحقق من صحة معرف السيرفر
      if (!newGuildId || isNaN(newGuildId)) {
        await interaction.editReply({ content: "❌ معرف السيرفر غير صالح. يرجى إدخال معرف صالح." });
        return;
      }

      // الحصول على بيانات المستخدم
      const userData = await db.get(interaction.user.id);
      if (!userData) {
        await interaction.editReply({ content: "❌ لم يتم العثور على بيانات المستخدم" });
        return;
      }

      // البحث عن الاشتراك
      const subscription = userData.find(s => s.id === subId);
      if (!subscription) {
        await interaction.editReply({ content: "❌ لم يتم العثور على الاشتراك" });
        return;
      }

      // التحقق مما إذا كان السيرفر الجديد هو نفس السيرفر الحالي
      if (newGuildId === subscription.guildID) {
        await interaction.editReply({ content: "❌ البوتات موجودة بالفعل في هذا السيرفر. يرجى إدخال معرف سيرفر مختلف." });
        return;
      }

      try {
        // تحديث معرف السيرفر في الاشتراك
        const subIndex = userData.findIndex(s => s.id === subId);
        userData[subIndex].guildID = newGuildId;

        // تحديث قاعدة البيانات
        await db.set(interaction.user.id, userData);

        // تحديث ملف الاشتراكات مباشرة
        try {
          const fs = require('fs');
          const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

          if (subsData[interaction.user.id]) {
            const subIndex = subsData[interaction.user.id].findIndex(s => s.id === subId);
            if (subIndex !== -1) {
              subsData[interaction.user.id][subIndex].guildID = newGuildId;

              // حفظ الملف
              fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
              console.log(`Subscription ${subId} server transferred to ${newGuildId} for user ${interaction.user.id}`);
            }
          }
        } catch (error) {
          console.error(`Error updating subscriptions file:`, error);
        }

        // إعادة تشغيل البوتات
        await restartSub(userData[subIndex]);

        // إنشاء روابط دعوة للبوتات
        const links = userData[subIndex].bots.map(b =>
          `https://discord.com/oauth2/authorize?client_id=${b.botId}&permissions=8&scope=bot%20applications.commands&guild_id=${newGuildId}&disable_guild_select=true`
        );

        // إرسال رسالة النجاح
        await interaction.editReply({
          content: `✅ تم تغيير سيرفر الاشتراك بنجاح إلى \`${newGuildId}\`. سيتم إرسال روابط دعوة البوتات إليك في الخاص.`
        });

        // إرسال روابط الدعوة في الخاص
        const user = await client.users.fetch(interaction.user.id);
        const embed = new Discord.EmbedBuilder()
          .setColor("Green")
          .setTitle("روابط دعوة البوتات")
          .setDescription(`تم تغيير سيرفر الاشتراك \`${subId}\` إلى \`${newGuildId}\`. استخدم الروابط أدناه لدعوة البوتات إلى السيرفر الجديد.`)
          .setTimestamp();

        await user.send({ embeds: [embed] }).catch(() => { });

        // إرسال الروابط في رسائل منفصلة (10 روابط في كل رسالة)
        const n = 10;
        for (let i = 0; i < links.length; i += n) {
          const batch = links.slice(i, i + n);
          await user.send({ content: batch.join('\n') }).catch(() => { });
        }

      } catch (error) {
        console.error("Error transferring server:", error);
        await interaction.editReply({ content: `❌ حدث خطأ أثناء محاولة نقل البوتات: ${error.message}` });
      }
    }
    // معالجة نموذج نقل ملكية الاشتراك
    else if (modalId.startsWith('ownership_transfer_modal_')) {
      await interaction.deferReply({ ephemeral: true });

      // استخراج معرف الاشتراك من معرف النموذج
      const subId = modalId.replace('ownership_transfer_modal_', '');

      // الحصول على معرف المستخدم الجديد من النموذج
      const newOwnerId = interaction.fields.getTextInputValue('new_owner_id');

      // التحقق من صحة معرف المستخدم
      if (!newOwnerId || isNaN(newOwnerId)) {
        await interaction.editReply({ content: "❌ معرف المستخدم غير صالح. يرجى إدخال معرف صالح." });
        return;
      }

      // التحقق مما إذا كان المستخدم الجديد هو نفس المالك الحالي
      if (newOwnerId === interaction.user.id) {
        await interaction.editReply({ content: "❌ أنت بالفعل مالك هذا الاشتراك. يرجى إدخال معرف مستخدم مختلف." });
        return;
      }

      try {
        // التحقق من وجود المستخدم الجديد
        const newOwner = await client.users.fetch(newOwnerId).catch(() => null);
        if (!newOwner) {
          await interaction.editReply({ content: "❌ لم يتم العثور على المستخدم. يرجى التأكد من معرف المستخدم." });
          return;
        }

        // الحصول على بيانات المستخدم الحالي
        const userData = await db.get(interaction.user.id);
        if (!userData) {
          await interaction.editReply({ content: "❌ لم يتم العثور على بيانات المستخدم" });
          return;
        }

        // البحث عن الاشتراك
        const subIndex = userData.findIndex(s => s.id === subId);
        if (subIndex === -1) {
          await interaction.editReply({ content: "❌ لم يتم العثور على الاشتراك" });
          return;
        }

        const subscription = userData[subIndex];

        // الحصول على بيانات المستخدم الجديد
        let newUserData = await db.get(newOwnerId) || [];

        // إضافة الاشتراك إلى المستخدم الجديد
        newUserData.push(subscription);

        // حذف الاشتراك من المستخدم الحالي
        userData.splice(subIndex, 1);

        // تحديث قاعدة البيانات
        await db.set(interaction.user.id, userData);
        await db.set(newOwnerId, newUserData);

        // تحديث ملف الاشتراكات مباشرة
        try {
          const fs = require('fs');
          const subsData = JSON.parse(fs.readFileSync('./databases/subscriptions.json', 'utf8'));

          // إضافة الاشتراك للمستخدم الجديد
          if (!subsData[newOwnerId]) {
            subsData[newOwnerId] = [];
          }
          subsData[newOwnerId].push(subscription);

          // حذف الاشتراك من المستخدم الحالي
          if (subsData[interaction.user.id]) {
            const oldSubIndex = subsData[interaction.user.id].findIndex(s => s.id === subId);
            if (oldSubIndex !== -1) {
              subsData[interaction.user.id].splice(oldSubIndex, 1);
            }
          }

          // حفظ الملف
          fs.writeFileSync('./databases/subscriptions.json', JSON.stringify(subsData, null, 2));
          console.log(`Subscription ${subId} ownership transferred from ${interaction.user.id} to ${newOwnerId}`);
        } catch (error) {
          console.error(`Error updating subscriptions file:`, error);
        }

        // إرسال رسالة النجاح
        await interaction.editReply({
          content: `✅ تم نقل ملكية الاشتراك \`${subId}\` بنجاح إلى المستخدم <@${newOwnerId}>.`
        });

        // إرسال إشعار للمستخدم الجديد
        try {
          const newOwnerUser = await client.users.fetch(newOwnerId);
          const embed = new Discord.EmbedBuilder()
            .setColor("Green")
            .setTitle("تم نقل ملكية اشتراك إليك")
            .setDescription(`قام المستخدم <@${interaction.user.id}> بنقل ملكية الاشتراك \`${subId}\` إليك.`)
            .addFields([
              {
                name: "معرف الاشتراك",
                value: subscription.id,
                inline: true
              },
              {
                name: "عدد البوتات",
                value: `${subscription.bots.length}`,
                inline: true
              },
              {
                name: "نوع الاشتراك",
                value: `${subscription.bots.find(bot => bot.type?.toLowerCase() === "music") ? "music" : subscription.bots.map(bot => bot.type).join(", ")}`,
                inline: true
              },
              {
                name: "معرف السيرفر",
                value: `${subscription.guildID}`,
                inline: true
              },
              {
                name: "تاريخ الانتهاء",
                value: `<t:${Math.floor(subscription.endDate / 1000)}:R>`,
                inline: true
              }
            ])
            .setTimestamp();

          await newOwnerUser.send({ embeds: [embed] }).catch(() => { });
        } catch (error) {
          console.error("Error sending notification to new owner:", error);
        }

      } catch (error) {
        console.error("Error transferring ownership:", error);
        await interaction.editReply({ content: `❌ حدث خطأ أثناء محاولة نقل ملكية الاشتراك: ${error.message}` });
      }
    }
  }
});

client.login(config.token);