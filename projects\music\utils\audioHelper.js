const { generateDependencyReport } = require('@discordjs/voice');

/**
 * فحص إعدادات الصوت والمكتبات المطلوبة
 */
function checkAudioDependencies() {
  console.log('🔍 فحص مكتبات الصوت...');

  try {
    const report = generateDependencyReport();
    console.log('📊 تقرير مكتبات الصوت:');
    console.log(report);

    // فحص المكتبات المطلوبة
    const requiredLibs = [
      '@discordjs/voice',
      '@discordjs/opus',
      'ffmpeg-static',
      'opusscript',
      'sodium-native'
    ];

    const missingLibs = [];

    for (const lib of requiredLibs) {
      try {
        require(lib);
        console.log(`✅ ${lib} - متوفر`);
      } catch (error) {
        console.log(`❌ ${lib} - مفقود`);
        missingLibs.push(lib);
      }
    }

    if (missingLibs.length > 0) {
      console.log('\n⚠️ مكتبات مفقودة:');
      missingLibs.forEach(lib => console.log(`   - ${lib}`));
      console.log('\n💡 لتثبيت المكتبات المفقودة، استخدم:');
      console.log(`npm install ${missingLibs.join(' ')}`);
    } else {
      console.log('\n✅ جميع مكتبات الصوت متوفرة!');
    }

    return {
      allAvailable: missingLibs.length === 0,
      missingLibs,
      report
    };

  } catch (error) {
    console.error('❌ خطأ في فحص مكتبات الصوت:', error);
    return {
      allAvailable: false,
      missingLibs: [],
      error: error.message
    };
  }
}

/**
 * فحص اتصال القناة الصوتية
 * @param {VoiceChannel} voiceChannel - القناة الصوتية
 * @param {Client} client - عميل Discord
 * @returns {Object} نتيجة الفحص
 */
function checkVoiceConnection(voiceChannel, client) {
  const checks = {
    channelExists: !!voiceChannel,
    botInGuild: false,
    hasConnectPermission: false,
    hasSpeakPermission: false,
    channelNotFull: false,
    botCanJoin: false
  };

  if (!voiceChannel) {
    return {
      success: false,
      checks,
      message: 'القناة الصوتية غير موجودة'
    };
  }

  // فحص وجود البوت في السيرفر
  const botMember = voiceChannel.guild.members.cache.get(client.user.id);
  checks.botInGuild = !!botMember;

  if (!botMember) {
    return {
      success: false,
      checks,
      message: 'البوت غير موجود في السيرفر'
    };
  }

  // فحص الصلاحيات
  const permissions = voiceChannel.permissionsFor(botMember);
  checks.hasConnectPermission = permissions.has('Connect');
  checks.hasSpeakPermission = permissions.has('Speak');

  // فحص إذا كانت القناة ممتلئة
  checks.channelNotFull = voiceChannel.userLimit === 0 || voiceChannel.members.size < voiceChannel.userLimit;

  // فحص إذا كان البوت يستطيع الانضمام
  checks.botCanJoin = checks.hasConnectPermission && checks.hasSpeakPermission && checks.channelNotFull;

  const allChecks = Object.values(checks).every(check => check === true);

  let message = 'جميع الفحوصات نجحت';
  if (!checks.hasConnectPermission) message = 'البوت لا يملك صلاحية الاتصال';
  else if (!checks.hasSpeakPermission) message = 'البوت لا يملك صلاحية التحدث';
  else if (!checks.channelNotFull) message = 'القناة الصوتية ممتلئة';

  return {
    success: allChecks,
    checks,
    message,
    voiceChannel: {
      name: voiceChannel.name,
      id: voiceChannel.id,
      userLimit: voiceChannel.userLimit,
      memberCount: voiceChannel.members.size
    }
  };
}

/**
 * تشخيص مشاكل الصوت
 * @param {DisTube} distube - مثيل DisTube
 * @param {Guild} guild - السيرفر
 * @returns {Object} تقرير التشخيص
 */
function diagnoseAudioIssues(distube, guild) {
  const diagnosis = {
    timestamp: new Date().toISOString(),
    guild: {
      id: guild.id,
      name: guild.name
    },
    distube: {
      connected: false,
      hasQueue: false,
      queueLength: 0,
      currentSong: null,
      volume: null,
      paused: false
    },
    voice: {
      connected: false,
      channelId: null,
      channelName: null
    },
    issues: [],
    recommendations: []
  };

  try {
    // فحص DisTube
    const queue = distube.getQueue(guild.id);
    if (queue) {
      diagnosis.distube.hasQueue = true;
      diagnosis.distube.queueLength = queue.songs.length;
      diagnosis.distube.currentSong = queue.songs[0]?.name || null;
      diagnosis.distube.volume = queue.volume;
      diagnosis.distube.paused = queue.paused;

      // فحص الاتصال الصوتي
      if (queue.voiceChannel) {
        diagnosis.voice.connected = true;
        diagnosis.voice.channelId = queue.voiceChannel.id;
        diagnosis.voice.channelName = queue.voiceChannel.name;
      }
    }

    // تحديد المشاكل
    if (!diagnosis.distube.hasQueue) {
      diagnosis.issues.push('لا توجد قائمة تشغيل نشطة');
      diagnosis.recommendations.push('تشغيل أغنية لإنشاء قائمة تشغيل');
    }

    if (!diagnosis.voice.connected) {
      diagnosis.issues.push('البوت غير متصل بقناة صوتية');
      diagnosis.recommendations.push('التأكد من انضمام البوت للقناة الصوتية');
    }

    if (diagnosis.distube.paused) {
      diagnosis.issues.push('التشغيل متوقف مؤقتاً');
      diagnosis.recommendations.push('استخدام أمر resume لاستكمال التشغيل');
    }

    if (diagnosis.distube.volume === 0) {
      diagnosis.issues.push('مستوى الصوت صفر');
      diagnosis.recommendations.push('زيادة مستوى الصوت');
    }

  } catch (error) {
    diagnosis.issues.push(`خطأ في التشخيص: ${error.message}`);
    diagnosis.recommendations.push('إعادة تشغيل البوت');
  }

  return diagnosis;
}





/**
 * إصلاح مشاكل الصوت التلقائي
 * @param {DisTube} distube - مثيل DisTube
 * @param {VoiceChannel} voiceChannel - القناة الصوتية
 * @returns {Promise<Object>} نتيجة الإصلاح
 */
async function autoFixAudioIssues(distube, voiceChannel) {
  const fixes = [];
  const errors = [];

  try {
    // محاولة الانضمام للقناة الصوتية
    if (voiceChannel) {
      try {
        await distube.voices.join(voiceChannel);
        fixes.push('تم الانضمام للقناة الصوتية');
      } catch (error) {
        errors.push(`فشل في الانضمام للقناة: ${error.message}`);
      }
    }

    // فحص وإصلاح مستوى الصوت
    const queue = distube.getQueue(voiceChannel?.guild);
    if (queue) {
      if (queue.volume === 0) {
        queue.setVolume(50);
        fixes.push('تم تعيين مستوى الصوت إلى 50');
      }

      if (queue.paused) {
        queue.resume();
        fixes.push('تم استكمال التشغيل');
      }
    }

  } catch (error) {
    errors.push(`خطأ في الإصلاح التلقائي: ${error.message}`);
  }

  return {
    success: errors.length === 0,
    fixes,
    errors
  };
}

/**
 * مراقب الصوت لمنع الانقطاع المفاجئ
 * @param {DisTube} distube - مثيل DisTube
 * @param {Guild} guild - السيرفر
 * @returns {Object} مراقب الصوت
 */
function createAudioMonitor(distube, guild) {
  let monitorInterval;
  let lastCheckTime = Date.now();
  let consecutiveFailures = 0;

  const monitor = {
    start() {
      console.log(`🔍 بدء مراقبة الصوت للسيرفر: ${guild.name}`);

      monitorInterval = setInterval(async () => {
        try {
          const queue = distube.getQueue(guild.id);
          const currentTime = Date.now();

          if (queue && queue.playing) {
            // فحص حالة الاتصال الصوتي
            const voiceConnection = distube.voices.get(guild.id);

            if (!voiceConnection) {
              console.warn(`⚠️ فقدان الاتصال الصوتي في ${guild.name}`);
              await this.handleDisconnection(queue);
              return;
            }

            // فحص حالة التشغيل
            if (queue.currentTime === 0 && currentTime - lastCheckTime > 10000) {
              console.warn(`⚠️ توقف التشغيل في ${guild.name}`);
              consecutiveFailures++;

              if (consecutiveFailures >= 3) {
                await this.handlePlaybackFailure(queue);
                consecutiveFailures = 0;
              }
            } else {
              consecutiveFailures = 0;
            }

            // فحص مستوى الصوت
            if (queue.volume === 0) {
              console.warn(`⚠️ مستوى الصوت صفر في ${guild.name}`);
              queue.setVolume(50);
            }

            lastCheckTime = currentTime;
          }

        } catch (error) {
          console.error(`❌ خطأ في مراقبة الصوت: ${error.message}`);
        }
      }, 5000); // فحص كل 5 ثوان
    },

    stop() {
      if (monitorInterval) {
        clearInterval(monitorInterval);
        monitorInterval = null;
        console.log(`⏹️ توقف مراقبة الصوت للسيرفر: ${guild.name}`);
      }
    },

    async handleDisconnection(queue) {
      try {
        console.log(`🔄 محاولة إعادة الاتصال في ${guild.name}`);

        if (queue.voiceChannel) {
          await distube.voices.join(queue.voiceChannel);

          // إعادة تشغيل الأغنية الحالية
          if (queue.songs.length > 0) {
            const currentSong = queue.songs[0];
            await queue.seek(queue.currentTime || 0);
            console.log(`✅ تم إعادة الاتصال وتشغيل: ${currentSong.name}`);
          }
        }
      } catch (error) {
        console.error(`❌ فشل في إعادة الاتصال: ${error.message}`);
      }
    },

    async handlePlaybackFailure(queue) {
      try {
        console.log(`🔄 محاولة إصلاح مشكلة التشغيل في ${guild.name}`);

        // محاولة إعادة تشغيل الأغنية
        if (queue.songs.length > 0) {
          const currentSong = queue.songs[0];
          const currentTime = queue.currentTime || 0;

          // إيقاف وإعادة تشغيل
          queue.stop();

          setTimeout(async () => {
            try {
              await distube.play(queue.voiceChannel, currentSong.url, {
                textChannel: queue.textChannel,
                member: queue.songs[0].member
              });

              // العودة للوقت المحفوظ
              if (currentTime > 0) {
                setTimeout(() => {
                  try {
                    queue.seek(currentTime);
                  } catch (seekError) {
                    console.error('خطأ في العودة للوقت:', seekError.message);
                  }
                }, 2000);
              }

              console.log(`✅ تم إصلاح التشغيل: ${currentSong.name}`);
            } catch (retryError) {
              console.error(`❌ فشل في إعادة التشغيل: ${retryError.message}`);
            }
          }, 1000);
        }
      } catch (error) {
        console.error(`❌ فشل في إصلاح التشغيل: ${error.message}`);
      }
    }
  };

  return monitor;
}

/**
 * إعداد مراقبة شاملة للصوت
 * @param {DisTube} distube - مثيل DisTube
 * @returns {Object} مدير المراقبة
 */
function setupAudioMonitoring(distube) {
  const monitors = new Map();

  const manager = {
    addGuild(guild) {
      if (!monitors.has(guild.id)) {
        const monitor = createAudioMonitor(distube, guild);
        monitors.set(guild.id, monitor);
        monitor.start();
        console.log(`✅ تم إضافة مراقبة الصوت للسيرفر: ${guild.name}`);
      }
    },

    removeGuild(guildId) {
      const monitor = monitors.get(guildId);
      if (monitor) {
        monitor.stop();
        monitors.delete(guildId);
        console.log(`🗑️ تم إزالة مراقبة الصوت للسيرفر: ${guildId}`);
      }
    },

    stopAll() {
      monitors.forEach((monitor, guildId) => {
        monitor.stop();
      });
      monitors.clear();
      console.log('⏹️ تم إيقاف جميع مراقبات الصوت');
    },

    getStatus() {
      return {
        activeMonitors: monitors.size,
        guilds: Array.from(monitors.keys())
      };
    }
  };

  // إعداد أحداث DisTube للمراقبة التلقائية
  distube.on('playSong', (queue, song) => {
    manager.addGuild(queue.voiceChannel.guild);
  });

  distube.on('finish', (queue) => {
    // إزالة المراقبة بعد انتهاء القائمة
    setTimeout(() => {
      const currentQueue = distube.getQueue(queue.voiceChannel.guild.id);
      if (!currentQueue || currentQueue.songs.length === 0) {
        manager.removeGuild(queue.voiceChannel.guild.id);
      }
    }, 5000);
  });

  distube.on('disconnect', (queue) => {
    manager.removeGuild(queue.voiceChannel.guild.id);
  });

  return manager;
}

/**
 * إصلاح مشاكل الصوت المتقدمة
 * @param {DisTube} distube - مثيل DisTube
 * @param {Guild} guild - السيرفر
 * @returns {Promise<Object>} نتيجة الإصلاح
 */
async function advancedAudioFix(distube, guild) {
  const results = {
    checks: [],
    fixes: [],
    errors: []
  };

  try {
    const queue = distube.getQueue(guild.id);

    // فحص 1: وجود القائمة
    if (!queue) {
      results.checks.push('❌ لا توجد قائمة تشغيل نشطة');
      return results;
    }

    results.checks.push('✅ قائمة التشغيل موجودة');

    // فحص 2: الاتصال الصوتي
    const voiceConnection = distube.voices.get(guild.id);
    if (!voiceConnection) {
      results.checks.push('❌ لا يوجد اتصال صوتي');

      // محاولة إعادة الاتصال
      if (queue.voiceChannel) {
        try {
          await distube.voices.join(queue.voiceChannel);
          results.fixes.push('✅ تم إعادة الاتصال بالقناة الصوتية');
        } catch (error) {
          results.errors.push(`❌ فشل في إعادة الاتصال: ${error.message}`);
        }
      }
    } else {
      results.checks.push('✅ الاتصال الصوتي موجود');
    }

    // فحص 3: حالة التشغيل
    if (queue.paused) {
      results.checks.push('⏸️ التشغيل متوقف مؤقتاً');
      queue.resume();
      results.fixes.push('▶️ تم استكمال التشغيل');
    } else if (queue.playing) {
      results.checks.push('✅ التشغيل نشط');
    } else {
      results.checks.push('❌ التشغيل متوقف');

      // محاولة إعادة التشغيل
      if (queue.songs.length > 0) {
        try {
          queue.resume();
          results.fixes.push('🔄 تم محاولة إعادة التشغيل');
        } catch (error) {
          results.errors.push(`❌ فشل في إعادة التشغيل: ${error.message}`);
        }
      }
    }

    // فحص 4: مستوى الصوت
    if (queue.volume === 0) {
      results.checks.push('🔇 مستوى الصوت صفر');
      queue.setVolume(50);
      results.fixes.push('🔊 تم تعيين مستوى الصوت إلى 50');
    } else {
      results.checks.push(`🔊 مستوى الصوت: ${queue.volume}`);
    }

    // فحص 5: الأغاني في القائمة
    if (queue.songs.length === 0) {
      results.checks.push('📭 القائمة فارغة');
    } else {
      results.checks.push(`🎵 عدد الأغاني: ${queue.songs.length}`);
      results.checks.push(`🎶 الأغنية الحالية: ${queue.songs[0].name}`);
    }

  } catch (error) {
    results.errors.push(`❌ خطأ في الفحص المتقدم: ${error.message}`);
  }

  return results;
}

module.exports = {
  checkAudioDependencies,
  checkVoiceConnection,
  diagnoseAudioIssues,
  printDiagnosisReport,
  autoFixAudioIssues,
  createAudioMonitor,
  setupAudioMonitoring,
  advancedAudioFix
};
