const { generateDependencyReport } = require('@discordjs/voice');

/**
 * فحص إعدادات الصوت والمكتبات المطلوبة
 */
function checkAudioDependencies() {
  console.log('🔍 فحص مكتبات الصوت...');
  
  try {
    const report = generateDependencyReport();
    console.log('📊 تقرير مكتبات الصوت:');
    console.log(report);
    
    // فحص المكتبات المطلوبة
    const requiredLibs = [
      '@discordjs/voice',
      '@discordjs/opus',
      'ffmpeg-static',
      'opusscript',
      'sodium-native'
    ];
    
    const missingLibs = [];
    
    for (const lib of requiredLibs) {
      try {
        require(lib);
        console.log(`✅ ${lib} - متوفر`);
      } catch (error) {
        console.log(`❌ ${lib} - مفقود`);
        missingLibs.push(lib);
      }
    }
    
    if (missingLibs.length > 0) {
      console.log('\n⚠️ مكتبات مفقودة:');
      missingLibs.forEach(lib => console.log(`   - ${lib}`));
      console.log('\n💡 لتثبيت المكتبات المفقودة، استخدم:');
      console.log(`npm install ${missingLibs.join(' ')}`);
    } else {
      console.log('\n✅ جميع مكتبات الصوت متوفرة!');
    }
    
    return {
      allAvailable: missingLibs.length === 0,
      missingLibs,
      report
    };
    
  } catch (error) {
    console.error('❌ خطأ في فحص مكتبات الصوت:', error);
    return {
      allAvailable: false,
      missingLibs: [],
      error: error.message
    };
  }
}

/**
 * فحص اتصال القناة الصوتية
 * @param {VoiceChannel} voiceChannel - القناة الصوتية
 * @param {Client} client - عميل Discord
 * @returns {Object} نتيجة الفحص
 */
function checkVoiceConnection(voiceChannel, client) {
  const checks = {
    channelExists: !!voiceChannel,
    botInGuild: false,
    hasConnectPermission: false,
    hasSpeakPermission: false,
    channelNotFull: false,
    botCanJoin: false
  };
  
  if (!voiceChannel) {
    return {
      success: false,
      checks,
      message: 'القناة الصوتية غير موجودة'
    };
  }
  
  // فحص وجود البوت في السيرفر
  const botMember = voiceChannel.guild.members.cache.get(client.user.id);
  checks.botInGuild = !!botMember;
  
  if (!botMember) {
    return {
      success: false,
      checks,
      message: 'البوت غير موجود في السيرفر'
    };
  }
  
  // فحص الصلاحيات
  const permissions = voiceChannel.permissionsFor(botMember);
  checks.hasConnectPermission = permissions.has('Connect');
  checks.hasSpeakPermission = permissions.has('Speak');
  
  // فحص إذا كانت القناة ممتلئة
  checks.channelNotFull = voiceChannel.userLimit === 0 || voiceChannel.members.size < voiceChannel.userLimit;
  
  // فحص إذا كان البوت يستطيع الانضمام
  checks.botCanJoin = checks.hasConnectPermission && checks.hasSpeakPermission && checks.channelNotFull;
  
  const allChecks = Object.values(checks).every(check => check === true);
  
  let message = 'جميع الفحوصات نجحت';
  if (!checks.hasConnectPermission) message = 'البوت لا يملك صلاحية الاتصال';
  else if (!checks.hasSpeakPermission) message = 'البوت لا يملك صلاحية التحدث';
  else if (!checks.channelNotFull) message = 'القناة الصوتية ممتلئة';
  
  return {
    success: allChecks,
    checks,
    message,
    voiceChannel: {
      name: voiceChannel.name,
      id: voiceChannel.id,
      userLimit: voiceChannel.userLimit,
      memberCount: voiceChannel.members.size
    }
  };
}

/**
 * تشخيص مشاكل الصوت
 * @param {DisTube} distube - مثيل DisTube
 * @param {Guild} guild - السيرفر
 * @returns {Object} تقرير التشخيص
 */
function diagnoseAudioIssues(distube, guild) {
  const diagnosis = {
    timestamp: new Date().toISOString(),
    guild: {
      id: guild.id,
      name: guild.name
    },
    distube: {
      connected: false,
      hasQueue: false,
      queueLength: 0,
      currentSong: null,
      volume: null,
      paused: false
    },
    voice: {
      connected: false,
      channelId: null,
      channelName: null
    },
    issues: [],
    recommendations: []
  };
  
  try {
    // فحص DisTube
    const queue = distube.getQueue(guild.id);
    if (queue) {
      diagnosis.distube.hasQueue = true;
      diagnosis.distube.queueLength = queue.songs.length;
      diagnosis.distube.currentSong = queue.songs[0]?.name || null;
      diagnosis.distube.volume = queue.volume;
      diagnosis.distube.paused = queue.paused;
      
      // فحص الاتصال الصوتي
      if (queue.voiceChannel) {
        diagnosis.voice.connected = true;
        diagnosis.voice.channelId = queue.voiceChannel.id;
        diagnosis.voice.channelName = queue.voiceChannel.name;
      }
    }
    
    // تحديد المشاكل
    if (!diagnosis.distube.hasQueue) {
      diagnosis.issues.push('لا توجد قائمة تشغيل نشطة');
      diagnosis.recommendations.push('تشغيل أغنية لإنشاء قائمة تشغيل');
    }
    
    if (!diagnosis.voice.connected) {
      diagnosis.issues.push('البوت غير متصل بقناة صوتية');
      diagnosis.recommendations.push('التأكد من انضمام البوت للقناة الصوتية');
    }
    
    if (diagnosis.distube.paused) {
      diagnosis.issues.push('التشغيل متوقف مؤقتاً');
      diagnosis.recommendations.push('استخدام أمر resume لاستكمال التشغيل');
    }
    
    if (diagnosis.distube.volume === 0) {
      diagnosis.issues.push('مستوى الصوت صفر');
      diagnosis.recommendations.push('زيادة مستوى الصوت');
    }
    
  } catch (error) {
    diagnosis.issues.push(`خطأ في التشخيص: ${error.message}`);
    diagnosis.recommendations.push('إعادة تشغيل البوت');
  }
  
  return diagnosis;
}


  


/**
 * إصلاح مشاكل الصوت التلقائي
 * @param {DisTube} distube - مثيل DisTube
 * @param {VoiceChannel} voiceChannel - القناة الصوتية
 * @returns {Promise<Object>} نتيجة الإصلاح
 */
async function autoFixAudioIssues(distube, voiceChannel) {
  const fixes = [];
  const errors = [];
  
  try {
    // محاولة الانضمام للقناة الصوتية
    if (voiceChannel) {
      try {
        await distube.voices.join(voiceChannel);
        fixes.push('تم الانضمام للقناة الصوتية');
      } catch (error) {
        errors.push(`فشل في الانضمام للقناة: ${error.message}`);
      }
    }
    
    // فحص وإصلاح مستوى الصوت
    const queue = distube.getQueue(voiceChannel?.guild);
    if (queue) {
      if (queue.volume === 0) {
        queue.setVolume(50);
        fixes.push('تم تعيين مستوى الصوت إلى 50');
      }
      
      if (queue.paused) {
        queue.resume();
        fixes.push('تم استكمال التشغيل');
      }
    }
    
  } catch (error) {
    errors.push(`خطأ في الإصلاح التلقائي: ${error.message}`);
  }
  
  return {
    success: errors.length === 0,
    fixes,
    errors
  };
}

module.exports = {
  checkAudioDependencies,
  checkVoiceConnection,
  diagnoseAudioIssues,
  printDiagnosisReport,
  autoFixAudioIssues
};
